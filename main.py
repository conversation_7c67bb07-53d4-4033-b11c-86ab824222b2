# === BEGIN AUDIO PROVIDER SELECTION (must be before any kivy imports) ===
import os
import time
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# تعيين مستوى السجل لمكتبة kivy لتجاهل الأخطاء غير الهامة
kivy_logger = logging.getLogger('kivy')
kivy_logger.setLevel(logging.CRITICAL)  # تعيين مستوى أعلى لتجاهل المزيد من الأخطاء

# تعيين مستوى السجل لمكتبة ffpyplayer لتجاهل الأخطاء غير الهامة
ffpyplayer_logger = logging.getLogger('ffpyplayer')
ffpyplayer_logger.setLevel(logging.CRITICAL)  # تعيين مستوى أعلى لتجاهل المزيد من الأخطاء

# إضافة مرشح مخصص لتجاهل أخطاء محددة
class IgnoreSpecificErrorsFilter(logging.Filter):
    def filter(self, record):
        # تجاهل أخطاء تغيير تخطيط القنوات
        if "Channel layout change is not supported" in record.getMessage():
            return False
        # تجاهل أخطاء FFmpeg غير المنفذة
        if "Not yet implemented in FFmpeg" in record.getMessage():
            return False
        return True

# إضافة المرشح إلى سجلات kivy و ffpyplayer
kivy_logger.addFilter(IgnoreSpecificErrorsFilter())
ffpyplayer_logger.addFilter(IgnoreSpecificErrorsFilter())

# === SAFE IMPORTS WITH ERROR HANDLING ===
# استيراد المكتبات المخصصة مع معالجة الأخطاء
try:
    from custom_slider import CustomSlider
    CUSTOM_SLIDER_AVAILABLE = True
    logger.info("CustomSlider imported successfully")
except ImportError as e:
    CUSTOM_SLIDER_AVAILABLE = False
    logger.warning(f"CustomSlider not available: {e}")
    # Create a fallback CustomSlider class
    from kivy.uix.slider import Slider
    class CustomSlider(Slider):
        pass

try:
    from playing_indicator import PlayingIndicator
    PLAYING_INDICATOR_AVAILABLE = True
    logger.info("PlayingIndicator imported successfully")
except ImportError as e:
    PLAYING_INDICATOR_AVAILABLE = False
    logger.warning(f"PlayingIndicator not available: {e}")
    # Create a fallback PlayingIndicator class
    from kivy.uix.widget import Widget
    class PlayingIndicator(Widget):
        pass

try:
    from color_circle import ColorCircle
    COLOR_CIRCLE_AVAILABLE = True
    logger.info("ColorCircle imported successfully")
except ImportError as e:
    COLOR_CIRCLE_AVAILABLE = False
    logger.warning(f"ColorCircle not available: {e}")
    # Create a fallback ColorCircle class
    from kivy.uix.widget import Widget
    class ColorCircle(Widget):
        pass

# === SAFE AUDIO SETUP ===
def setup_audio_safely():
    """إعداد الصوت بشكل آمن للأندرويد"""
    try:
        # تجربة pygame أولاً (الأكثر استقراراً على الأندرويد)
        os.environ['KIVY_AUDIO'] = 'pygame'
        os.environ['SDL_AUDIODRIVER'] = 'android'
        os.environ['KIVY_AUDIO_BUFFER_SIZE'] = '4096'
        os.environ['SDL_AUDIO_FREQUENCY'] = '44100'
        logger.info("✅ Audio provider set to pygame for Android")
        return True
    except Exception as e:
        logger.warning(f"Failed to set pygame audio: {e}")
        try:
            # تجربة gstplayer كبديل
            os.environ['KIVY_AUDIO'] = 'gstplayer'
            logger.info("✅ Audio provider set to gstplayer")
            return True
        except Exception as e2:
            logger.error(f"Failed to set any audio provider: {e2}")
            return False

# تشغيل إعداد الصوت
audio_setup_success = setup_audio_safely()

# Add environment variable to improve Arabic text rendering
os.environ['KIVY_TEXT'] = 'pil'  # Use PIL for better text rendering

from kivy.config import Config
from kivy.utils import platform

# Set audio provider in the 'kivy' section
if not Config.has_section('kivy'):
    Config.add_section('kivy')

# Set NotoNaskhArabic as the default font for the entire application
try:
    Config.set('kivy', 'default_font', ['NotoNaskhArabic-VariableFont_wght', 'fonts/NotoNaskhArabic-VariableFont_wght.ttf'])
    logger.info("Default font set to NotoNaskhArabic")
except Exception as e:
    logger.warning(f"Failed to set default font: {e}")

# تعيين مزود الصوت إلى pygame فقط للأندرويد
if audio_setup_success:
    try:
        Config.set('kivy', 'audio', 'pygame')
        logger.info("✅ Kivy audio provider set to: pygame for Android")
    except Exception as e:
        logger.warning(f"Failed to set audio provider 'pygame' in config: {e}")
# === END AUDIO PROVIDER SELECTION ===

from kivy.metrics import dp, sp
from kivy.lang import Builder
from kivy.uix.boxlayout import BoxLayout
from kivymd.uix.list import OneLineListItem
from kivy.uix.screenmanager import ScreenManager, Screen, SlideTransition, FadeTransition, CardTransition, SwapTransition
from kivy.clock import Clock, mainthread
from kivy.core.audio import SoundLoader
from kivy.properties import ObjectProperty, NumericProperty, BooleanProperty, ListProperty, StringProperty
from kivymd.uix.dialog import MDDialog
from kivymd.uix.button import MDFlatButton
from kivymd.app import MDApp
from kivymd.uix.button import MDRaisedButton, MDIconButton
from kivymd.uix.card import MDCard
from kivymd.uix.label import MDLabel
from kivymd.uix.list import OneLineAvatarIconListItem, ILeftBodyTouch, IRightBodyTouch
from kivymd.uix.slider import MDSlider
from kivymd.uix.toolbar import MDTopAppBar
from kivymd.uix.toolbar.toolbar import ActionTopAppBarButton
from kivymd.uix.navigationdrawer import MDNavigationLayout, MDNavigationDrawer
from kivymd.uix.filemanager import MDFileManager
from kivymd.uix.menu import MDDropdownMenu
from kivymd.uix.snackbar import Snackbar

# === SAFE IMPORTS FOR ADDITIONAL MODULES ===
try:
    from download_manager import DownloadManager
    DOWNLOAD_MANAGER_AVAILABLE = True
    logger.info("DownloadManager imported successfully")
except ImportError as e:
    DOWNLOAD_MANAGER_AVAILABLE = False
    logger.warning(f"DownloadManager not available: {e}")
    # Create a fallback DownloadManager class
    class DownloadManager:
        def __init__(self):
            pass
        def download_audio(self, url, callback=None):
            if callback: callback(False, "Download manager not available")
        def get_download_progress(self): return 0
        def cancel_download(self): pass

try:
    from audio_enhancer import AudioEnhancer
    AUDIO_ENHANCER_AVAILABLE = True
    logger.info("AudioEnhancer imported successfully")
except ImportError as e:
    AUDIO_ENHANCER_AVAILABLE = False
    logger.warning(f"AudioEnhancer not available: {e}")
    # Create a fallback AudioEnhancer class
    class AudioEnhancer:
        def __init__(self, *args, **kwargs):
            pass
        def enhance_audio(self, path):
            return path
        def apply_equalizer(self, path, settings):
            return path

try:
    from download_screen import DownloadScreen
    DOWNLOAD_SCREEN_AVAILABLE = True
    logger.info("DownloadScreen imported successfully")
except ImportError as e:
    DOWNLOAD_SCREEN_AVAILABLE = False
    logger.warning(f"DownloadScreen not available: {e}")
    # Create a fallback DownloadScreen class
    from kivy.uix.screenmanager import Screen
    class DownloadScreen(Screen):
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
        def show_download_list(self): pass
        def add_download(self, url): pass

try:
    from search_screen import SearchScreen
    SEARCH_SCREEN_AVAILABLE = True
    logger.info("SearchScreen imported successfully")
except ImportError as e:
    SEARCH_SCREEN_AVAILABLE = False
    logger.warning(f"SearchScreen not available: {e}")
    # Create a fallback SearchScreen class
    from kivy.uix.screenmanager import Screen
    class SearchScreen(Screen):
        def __init__(self, **kwargs):
            super().__init__(**kwargs)
        def search_online(self, query): pass
        def play_online_song(self, url): pass

# === SAFE NETWORK IMPORTS ===
try:
    import requests
    import urllib3
    from urllib.parse import urlparse, quote
    NETWORK_AVAILABLE = True
    logger.info("Network libraries imported successfully")
except ImportError as e:
    NETWORK_AVAILABLE = False
    logger.warning(f"Network libraries not available: {e}")
    # Create fallback
    class requests:
        @staticmethod
        def get(url, **kwargs):
            class Response:
                status_code = 404
                text = ""
                def json(self): return {}
            return Response()
    def urlparse(url): return type('obj', (object,), {'netloc': '', 'path': ''})()
    def quote(s): return s

# === SAFE PLYER IMPORTS ===
try:
    from plyer import notification, vibrator, battery
    PLYER_AVAILABLE = True
    logger.info("Plyer imported successfully")
except ImportError as e:
    PLYER_AVAILABLE = False
    logger.warning(f"Plyer not available: {e}")
    # Create fallback
    class notification:
        @staticmethod
        def notify(title="", message="", timeout=5): pass
    class vibrator:
        @staticmethod
        def vibrate(time=0.1): pass
    class battery:
        @staticmethod
        def status(): return {'percentage': 100}

# === SAFE ANDROID PERMISSIONS ===
try:
    from android.permissions import request_permissions, Permission, check_permission
    ANDROID_PERMISSIONS_AVAILABLE = True
    logger.info("Android permissions imported successfully")
except ImportError:
    ANDROID_PERMISSIONS_AVAILABLE = False
    logger.warning("Android permissions not available")
    # Fallback if android.permissions is not available during build
    request_permissions = lambda perms, callback=None: None
    class Permission:
        READ_EXTERNAL_STORAGE = "android.permission.READ_EXTERNAL_STORAGE"
        WRITE_EXTERNAL_STORAGE = "android.permission.WRITE_EXTERNAL_STORAGE"
        FOREGROUND_SERVICE = "android.permission.FOREGROUND_SERVICE"
        INTERNET = "android.permission.INTERNET"
        MANAGE_EXTERNAL_STORAGE = "android.permission.MANAGE_EXTERNAL_STORAGE"
        WAKE_LOCK = "android.permission.WAKE_LOCK"
        SYSTEM_ALERT_WINDOW = "android.permission.SYSTEM_ALERT_WINDOW"
    def check_permission(perm): return True

# === BACKGROUND SERVICE IMPORTS ===
try:
    from kivy.logger import Logger
    from android.broadcast import BroadcastReceiver
    from jnius import autoclass, PythonJavaClass, java_method
    BACKGROUND_SERVICE_AVAILABLE = True
    logger.info("Background service components imported successfully")
except ImportError:
    BACKGROUND_SERVICE_AVAILABLE = False
    logger.warning("Background service components not available")
    # Fallback classes
    class BroadcastReceiver: pass
    def autoclass(name): return None
    def PythonJavaClass(): pass
    def java_method(signature): return lambda f: f

# === ENHANCED PERMISSION REQUEST FUNCTION ===
def request_permissions_safely():
    """طلب الأذونات بشكل آمن مع معالجة محسنة"""
    if not ANDROID_PERMISSIONS_AVAILABLE:
        logger.info("Android permissions not available, skipping permission request")
        return

    try:
        # قائمة الأذونات المطلوبة مع أولوية
        essential_permissions = [
            Permission.READ_EXTERNAL_STORAGE,
            Permission.WRITE_EXTERNAL_STORAGE,
            Permission.INTERNET
        ]

        optional_permissions = [
            Permission.FOREGROUND_SERVICE,
            Permission.WAKE_LOCK,
            Permission.MANAGE_EXTERNAL_STORAGE
        ]

        # طلب الأذونات الأساسية أولاً
        logger.info("🔐 Checking essential permissions...")
        missing_essential = []
        for perm in essential_permissions:
            try:
                if not check_permission(perm):
                    missing_essential.append(perm)
                    logger.info(f"❌ Missing essential permission: {perm}")
                else:
                    logger.info(f"✅ Essential permission granted: {perm}")
            except Exception as e:
                logger.warning(f"Error checking permission {perm}: {e}")
                missing_essential.append(perm)

        # طلب الأذونات الاختيارية
        missing_optional = []
        for perm in optional_permissions:
            try:
                if not check_permission(perm):
                    missing_optional.append(perm)
                    logger.info(f"⚠️ Missing optional permission: {perm}")
                else:
                    logger.info(f"✅ Optional permission granted: {perm}")
            except Exception as e:
                logger.warning(f"Error checking optional permission {perm}: {e}")

        # طلب الأذونات المفقودة
        all_missing = missing_essential + missing_optional

        if all_missing:
            logger.info(f"🔐 Requesting {len(all_missing)} permissions...")

            def permission_callback(permissions, results):
                try:
                    granted_count = 0
                    denied_count = 0

                    for perm, result in zip(permissions, results):
                        if result:
                            logger.info(f"✅ Permission granted: {perm}")
                            granted_count += 1
                        else:
                            logger.warning(f"❌ Permission denied: {perm}")
                            denied_count += 1

                    logger.info(f"📊 Permission results: {granted_count} granted, {denied_count} denied")

                    # إظهار رسالة للمستخدم
                    if denied_count > 0:
                        show_permission_message(denied_count)

                except Exception as e:
                    logger.error(f"Error in permission callback: {e}")

            # طلب جميع الأذونات
            request_permissions(all_missing, permission_callback)

        else:
            logger.info("✅ All permissions already granted")

    except Exception as e:
        logger.error(f"❌ Error requesting permissions: {e}")

def show_permission_message(denied_count):
    """عرض رسالة للمستخدم حول الأذونات المرفوضة"""
    try:
        from kivy.clock import Clock

        def show_message(dt):
            try:
                # محاولة إظهار رسالة باستخدام Snackbar
                from kivymd.uix.snackbar import Snackbar
                message = f"تم رفض {denied_count} من الأذونات. قد تحتاج لتفعيلها من الإعدادات."
                snackbar = Snackbar(text=message, duration=5)
                snackbar.open()
            except Exception as e:
                logger.warning(f"Could not show permission message: {e}")

        Clock.schedule_once(show_message, 1)

    except Exception as e:
        logger.error(f"Error showing permission message: {e}")

# === FORCE PERMISSION REQUEST ON STARTUP ===
def force_permission_request():
    """إجبار طلب الأذونات عند بدء التطبيق"""
    if not ANDROID_PERMISSIONS_AVAILABLE:
        return

    try:
        from kivy.clock import Clock

        def delayed_permission_request(dt):
            logger.info("🔐 Force requesting permissions...")
            request_permissions_safely()

        # طلب الأذونات بعد ثانية من بدء التطبيق
        Clock.schedule_once(delayed_permission_request, 1.0)

    except Exception as e:
        logger.error(f"Error in force permission request: {e}")

from mutagen import File
from mutagen.id3 import ID3
from mutagen.oggvorbis import OggVorbis
from mutagen.mp4 import MP4
from mutagen.asf import ASF
from mutagen.aiff import AIFF
from mutagen.wave import WAVE
from mutagen.oggopus import OggOpus
import traceback
import os
import json
import re
import subprocess
from kivy.uix.dropdown import DropDown
from kivy.uix.button import Button
from kivy.uix.image import Image, AsyncImage
from kivy.core.window import Window
from kivy.utils import platform
from kivy.animation import Animation
from kivy.factory import Factory
from kivy.graphics import Color, PushMatrix, PopMatrix, Rotate
from kivy.uix.progressbar import ProgressBar
from kivy.uix.behaviors import ButtonBehavior
from kivy.graphics import Color, Line, Ellipse
from kivy.uix.relativelayout import RelativeLayout
from kivy.core.image import Image as CoreImage
from io import BytesIO
from base64 import b64decode
import hashlib
import mutagen
from mutagen.mp3 import MP3
from mutagen.flac import FLAC

# === SAFE ARABIC TEXT SUPPORT ===
try:
    from arabic_utils import reshape_arabic_text, contains_arabic, get_display_text, ARABIC_SUPPORT
    ARABIC_UTILS_AVAILABLE = True
    logger.info("Arabic text utilities imported successfully")
except ImportError as e:
    ARABIC_UTILS_AVAILABLE = False
    logger.warning(f"Arabic text utilities not available: {e}")
    # Define fallback functions
    def contains_arabic(text): return False
    def reshape_arabic_text(text): return text
    def get_display_text(text, always_process=False): return text
    ARABIC_SUPPORT = False

# === SAFE PERFORMANCE OPTIMIZER ===
try:
    from performance_optimizer import PerformanceOptimizer
    PERFORMANCE_OPTIMIZER_AVAILABLE = True
    logger.info("PerformanceOptimizer imported successfully")
except ImportError as e:
    PERFORMANCE_OPTIMIZER_AVAILABLE = False
    logger.warning(f"PerformanceOptimizer not available: {e}")
    # Create a fallback PerformanceOptimizer class
    class PerformanceOptimizer:
        def __init__(self, app_instance):
            self.app = app_instance
        def optimize_performance(self): pass
        def cleanup_memory(self): pass
from kivy.core.window import Window
from kivy.core.text import LabelBase
import sys
import logging

# Additional logging setup

# إعدادات النافذة للأندرويد - ملء الشاشة
Window.fullscreen = 'auto'  # ملء الشاشة تلقائياً على الأندرويد

ActionTopAppBarButton.radius = [dp(24)]
MDIconButton.radius = [dp(24)]

# Register application font
def register_system_font():
    try:
        # Use only the bundled font in the fonts folder
        font_file = 'NotoNaskhArabic-VariableFont_wght.ttf'
        font_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fonts', font_file)

        if os.path.exists(font_path):
            logger.info(f"Found font file at: {font_path}")

            # استخدام اسم الملف الكامل كاسم للخط لتجنب مشاكل العثور على الملف
            font_name = 'NotoNaskhArabic-VariableFont_wght'

            # Register the Arabic font with all styles to ensure proper rendering
            LabelBase.register(
                name=font_name,
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            # Register the font as the default font
            LabelBase.register(
                name='default',
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            # Set additional font properties for Arabic text
            from kivy.core.text import Label as CoreLabel
            CoreLabel.register(font_name, font_path)
            CoreLabel.register('default', font_path)

            # Override Roboto font with our font to ensure it's used everywhere
            LabelBase.register(
                name='Roboto',
                fn_regular=font_path,
                fn_bold=font_path,
                fn_italic=font_path,
                fn_bolditalic=font_path
            )

            logger.info(f"Successfully registered {font_name} as the default font")
            return font_name
        else:
            logger.error(f"Font file not found at: {font_path}")
            return 'Roboto'  # Fallback to Roboto if font file not found
    except Exception as e:
        logger.error(f"Error registering system font: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return 'Roboto'

# Call the function to register system font at startup
system_font = register_system_font()

# -- RotatingImage widget for KV rotation usage --
class RotatingImage(AsyncImage):
    """
    AsyncImage extension adding an 'angle' property (NumericProperty)
    for continuous rotation via Animation.
    """
    angle = NumericProperty(0)

    def __init__(self, **kwargs):
        self._rotate_instruction = None
        super(RotatingImage, self).__init__(**kwargs)
        Clock.schedule_once(self._setup_rotation, 0)

    def _setup_rotation(self, dt):
        """Setup rotation instructions after widget is fully initialized"""
        with self.canvas.before:
            PushMatrix()
            self._rotate_instruction = Rotate(angle=0, origin=self.center)
        with self.canvas.after:
            PopMatrix()
        self.bind(pos=self._update_rotate, size=self._update_rotate)
        self.bind(angle=self._update_rotate)

    def _update_rotate(self, *args):
        """Update rotation origin and angle when properties change"""
        if self._rotate_instruction:
            self._rotate_instruction.origin = self.center
            self._rotate_instruction.angle = self.angle

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

# Register with Factory so KV can instantiate RotatingImage without additional import
Factory.register('RotatingImage', cls=RotatingImage)

# Import necessary graphics classes
from kivy.graphics import Color, Rectangle, Line, Ellipse
from kivy.uix.widget import Widget

# Import necessary graphics classes
from math import cos, sin, radians

# Circular progress bar for play/pause button
class CircularProgressBar(Widget):
    """A circular progress bar widget that shows progress as an arc around a circle"""
    value = NumericProperty(0)
    max = NumericProperty(100)
    thickness = NumericProperty(dp(3))
    color = ListProperty([0.2, 0.7, 1, 1])  # Default color: light blue

    def __init__(self, **kwargs):
        super(CircularProgressBar, self).__init__(**kwargs)

        # Schedule initial drawing
        Clock.schedule_once(self.draw, 0)

        # Bind properties to update method
        self.bind(
            value=self.draw,
            max=self.draw,
            pos=self.draw,
            size=self.draw,
            thickness=self.draw,
            color=self.draw
        )

    def draw(self, *args):
        """Draw the circular progress bar"""
        # Clear the canvas
        self.canvas.clear()

        # Get dimensions
        center_x = self.center_x
        center_y = self.center_y
        radius = min(self.width, self.height) / 2 - self.thickness / 2

        # Calculate progress
        if self.max <= 0:
            progress_angle = 0
        else:
            progress_ratio = max(0, min(1, self.value / self.max))
            progress_angle = 360 * progress_ratio

        with self.canvas:
            # Draw background circle (track)
            Color(0.3, 0.3, 0.3, 0.3)
            Line(circle=(center_x, center_y, radius), width=self.thickness)

            # Draw progress arc if there is progress
            if progress_angle > 0:
                Color(*self.color)

                # Draw the arc manually using line segments
                points = []
                segments = int(max(3, progress_angle / 5))  # At least 3 segments, more for larger angles

                for i in range(segments + 1):
                    angle = radians(i * progress_angle / segments)
                    points.extend([
                        center_x + radius * cos(angle),
                        center_y + radius * sin(angle)
                    ])

                Line(points=points, width=self.thickness)

    def update_progress(self, *args):
        """Update method for compatibility with existing code"""
        self.draw(*args)

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

# Simple progress bar that doesn't rely on MDSlider
class SimpleProgressBar(BoxLayout):
    """A simple progress bar that reliably shows audio progress"""
    value = NumericProperty(0)
    max = NumericProperty(100)

    def __init__(self, **kwargs):
        super(SimpleProgressBar, self).__init__(**kwargs)
        self.orientation = 'horizontal'
        self.size_hint_y = None
        self.height = dp(30)
        self.padding = [dp(5), dp(10)]

        # Create the progress background
        self.background = BoxLayout(size_hint=(1, 1))
        self.background.canvas.before.add(Color(0.3, 0.3, 0.3, 1))
        self.background.canvas.before.add(Rectangle(pos=self.background.pos, size=self.background.size))

        # Create the progress indicator
        self.progress = BoxLayout(size_hint=(None, 1), width=0)
        self.progress.canvas.before.add(Color(0.2, 0.7, 1, 1))
        self.progress_rect = Rectangle(pos=self.progress.pos, size=self.progress.size)
        self.progress.canvas.before.add(self.progress_rect)

        # Add widgets to layout
        self.add_widget(self.background)
        self.background.add_widget(self.progress)

        # Bind to property changes
        self.bind(value=self.update_progress)
        self.bind(max=self.update_progress)
        self.bind(size=self.update_progress)
        self.bind(pos=self.update_progress)

    def update_progress(self, *args):
        """Update the progress bar based on value/max"""
        if self.max <= 0:
            progress_width = 0
        else:
            progress_width = (self.value / self.max) * self.width

        self.progress.width = progress_width
        self.progress_rect.pos = self.progress.pos
        self.progress_rect.size = self.progress.size

    def on_touch_down(self, touch):
        """Handle touch down event for seeking"""
        if self.collide_point(*touch.pos):
            self.seek_to_position(touch.x)
            return True
        return super(SimpleProgressBar, self).on_touch_down(touch)

    def on_touch_move(self, touch):
        """Handle touch move event for seeking"""
        if self.collide_point(*touch.pos):
            self.seek_to_position(touch.x)
            return True
        return super(SimpleProgressBar, self).on_touch_move(touch)

    def seek_to_position(self, x_pos):
        """Convert touch position to progress value"""
        relative_x = x_pos - self.x
        if self.width > 0:
            new_value = (relative_x / self.width) * self.max
            new_value = max(0, min(new_value, self.max))
            self.value = new_value

            # Call parent's seek method if available
            if hasattr(self.parent, 'parent') and hasattr(self.parent.parent, 'seek_to'):
                self.parent.parent.seek_to(new_value)

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

# Register with Factory so KV can use it
Factory.register('SimpleProgressBar', cls=SimpleProgressBar)

# Register CircularProgressBar with Factory so KV can use it
Factory.register('CircularProgressBar', cls=CircularProgressBar)

# Gradient Background Widget
class GradientBackground(Widget):
    """خلفية متدرجة مخصصة للتطبيق"""
    colors = ListProperty([[0.1, 0.1, 0.1, 1], [0.2, 0.2, 0.2, 1]])
    direction = StringProperty('vertical')  # 'vertical', 'horizontal', 'diagonal'

    def __init__(self, **kwargs):
        super(GradientBackground, self).__init__(**kwargs)
        self.bind(size=self.update_gradient, pos=self.update_gradient)
        self.bind(colors=self.update_gradient, direction=self.update_gradient)
        Clock.schedule_once(self.update_gradient, 0)

    def update_gradient(self, *args):
        """تحديث الخلفية المتدرجة"""
        self.canvas.before.clear()

        if len(self.colors) < 2:
            return

        with self.canvas.before:
            # إنشاء تدرج باستخدام مستطيلات متعددة
            num_steps = 50  # عدد الخطوات في التدرج

            if self.direction == 'vertical':
                step_height = self.height / num_steps
                for i in range(num_steps):
                    # حساب اللون المتدرج
                    ratio = i / (num_steps - 1)
                    color = self.interpolate_colors(self.colors[0], self.colors[1], ratio)

                    Color(*color)
                    Rectangle(
                        pos=(self.x, self.y + i * step_height),
                        size=(self.width, step_height + 1)  # +1 لتجنب الفجوات
                    )

            elif self.direction == 'horizontal':
                step_width = self.width / num_steps
                for i in range(num_steps):
                    ratio = i / (num_steps - 1)
                    color = self.interpolate_colors(self.colors[0], self.colors[1], ratio)

                    Color(*color)
                    Rectangle(
                        pos=(self.x + i * step_width, self.y),
                        size=(step_width + 1, self.height)
                    )

            elif self.direction == 'diagonal':
                # تدرج قطري - من الزاوية اليسرى السفلى إلى اليمنى العلوية
                step_size = max(self.width, self.height) / num_steps
                for i in range(num_steps):
                    ratio = i / (num_steps - 1)
                    color = self.interpolate_colors(self.colors[0], self.colors[1], ratio)

                    Color(*color)
                    # رسم مستطيلات قطرية
                    Rectangle(
                        pos=(self.x + i * step_size * 0.5, self.y + i * step_size * 0.5),
                        size=(self.width, self.height)
                    )

    def interpolate_colors(self, color1, color2, ratio):
        """دمج لونين بنسبة معينة"""
        return [
            color1[0] + (color2[0] - color1[0]) * ratio,
            color1[1] + (color2[1] - color1[1]) * ratio,
            color1[2] + (color2[2] - color1[2]) * ratio,
            color1[3] + (color2[3] - color1[3]) * ratio
        ]

# Pattern Background Widget
class PatternBackground(Widget):
    """خلفية بنمط هندسي للتطبيق"""
    base_color = ListProperty([0.1, 0.1, 0.1, 1])
    pattern_color = ListProperty([0.2, 0.2, 0.2, 0.3])
    pattern_type = StringProperty('dots')  # 'dots', 'lines', 'grid', 'waves'

    def __init__(self, **kwargs):
        super(PatternBackground, self).__init__(**kwargs)
        self.bind(size=self.update_pattern, pos=self.update_pattern)
        self.bind(base_color=self.update_pattern, pattern_color=self.update_pattern)
        self.bind(pattern_type=self.update_pattern)
        Clock.schedule_once(self.update_pattern, 0)

    def update_pattern(self, *args):
        """تحديث نمط الخلفية"""
        self.canvas.before.clear()

        with self.canvas.before:
            # رسم الخلفية الأساسية
            Color(*self.base_color)
            Rectangle(pos=self.pos, size=self.size)

            # رسم النمط
            Color(*self.pattern_color)

            if self.pattern_type == 'dots':
                self.draw_dots_pattern()
            elif self.pattern_type == 'lines':
                self.draw_lines_pattern()
            elif self.pattern_type == 'grid':
                self.draw_grid_pattern()
            elif self.pattern_type == 'waves':
                self.draw_waves_pattern()

    def draw_dots_pattern(self):
        """رسم نمط النقاط"""
        dot_spacing = dp(30)
        dot_size = dp(3)

        for x in range(int(self.x), int(self.x + self.width), int(dot_spacing)):
            for y in range(int(self.y), int(self.y + self.height), int(dot_spacing)):
                Ellipse(pos=(x - dot_size/2, y - dot_size/2), size=(dot_size, dot_size))

    def draw_lines_pattern(self):
        """رسم نمط الخطوط"""
        line_spacing = dp(20)

        for x in range(int(self.x), int(self.x + self.width), int(line_spacing)):
            Line(points=[x, self.y, x, self.y + self.height], width=1)

    def draw_grid_pattern(self):
        """رسم نمط الشبكة"""
        grid_spacing = dp(25)

        # خطوط عمودية
        for x in range(int(self.x), int(self.x + self.width), int(grid_spacing)):
            Line(points=[x, self.y, x, self.y + self.height], width=0.5)

        # خطوط أفقية
        for y in range(int(self.y), int(self.y + self.height), int(grid_spacing)):
            Line(points=[self.x, y, self.x + self.width, y], width=0.5)

    def draw_waves_pattern(self):
        """رسم نمط الموجات"""
        import math
        wave_height = dp(10)
        wave_length = dp(40)

        for y in range(int(self.y), int(self.y + self.height), int(wave_height * 3)):
            points = []
            for x in range(int(self.x), int(self.x + self.width), 5):
                wave_y = y + wave_height * math.sin(2 * math.pi * x / wave_length)
                points.extend([x, wave_y])

            if len(points) >= 4:
                Line(points=points, width=1)

# Register the new widgets with Factory
Factory.register('GradientBackground', cls=GradientBackground)
Factory.register('PatternBackground', cls=PatternBackground)

# -- Add angle property to AsyncImage for compatibility --
# Define the angle property before any binding occurs
AsyncImage.angle = NumericProperty(0)

# This ensures that regular AsyncImage can have an angle property
# without the rotation functionality, for API compatibility

# No longer using MarqueeSongTitle, using Animation directly

class FontSelectionItem(OneLineListItem):
    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class LeftContainer(ILeftBodyTouch, MDLabel):
    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class RightContainer(IRightBodyTouch, MDIconButton):
    is_playing = BooleanProperty(False)

    def __init__(self, **kwargs):
        self.is_playing = kwargs.pop('is_playing', False)
        super(RightContainer, self).__init__(**kwargs)
        self._effect_anim = None
        self._effect_size = 0

        # Set up canvas for visual effect
        with self.canvas.before:
            self.effect_color = Color(0, 1, 0, 0.5)
            self.effect_circle = Ellipse(pos=(0, 0), size=(0, 0))

        # Start animation if this is the current playing song
        if self.is_playing:
            Clock.schedule_once(self.start_effect_animation, 0.1)

        # Bind to property changes
        self.bind(pos=self._update_effect_pos, size=self._update_effect_pos)
        self.bind(is_playing=self._on_is_playing_changed)

    def _update_effect_pos(self, *args):
        """Update the position of the effect circle."""
        if hasattr(self, 'effect_circle'):
            # Position the effect circle to the left of the button
            self.effect_circle.pos = (
                self.x - dp(25),  # Position to the left of the button
                self.y + (self.height - self._effect_size) / 2
            )

    def _on_is_playing_changed(self, instance, value):
        """Handle changes to is_playing property."""
        if value:
            self.start_effect_animation()
        else:
            self.stop_effect_animation()

    def start_effect_animation(self, dt=None):
        """Start the pulsating animation for the visual effect."""
        # Cancel any existing animation
        if self._effect_anim:
            self._effect_anim.cancel(self)

        # Set initial size
        self._effect_size = dp(20)
        self.effect_circle.size = (self._effect_size, self._effect_size)
        self._update_effect_pos()

        # Create animation for pulsating effect
        anim1 = Animation(_effect_size=dp(30), duration=0.6)
        anim2 = Animation(_effect_size=dp(20), duration=0.6)

        # Chain animations
        anim = anim1 + anim2
        anim.repeat = True

        # Bind update function
        anim.bind(on_progress=self._update_effect)

        # Start animation
        self._effect_anim = anim
        anim.start(self)

    def _update_effect(self, animation, instance, progress):
        """Update the effect based on animation progress."""
        if hasattr(self, 'effect_circle'):
            self.effect_circle.size = (self._effect_size, self._effect_size)
            self._update_effect_pos()

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def stop_effect_animation(self):
        """Stop the visual effect animation."""
        if self._effect_anim:
            self._effect_anim.cancel(self)
            self._effect_anim = None

        # Hide the effect
        if hasattr(self, 'effect_circle'):
            self.effect_circle.size = (0, 0)

# تم إزالة فئة IconListItem المخصصة لأنها تسبب مشاكل

class MainScreen(Screen):
    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class NowPlayingScreen(Screen):
    # Removed touch_start_x property as it's no longer needed

    def on_touch_down(self, touch):
        if super(NowPlayingScreen, self).on_touch_down(touch):
            return True
        # Removed swipe tracking code
        return False

    def on_touch_up(self, touch):
        if super(NowPlayingScreen, self).on_touch_up(touch):
            return True
        # Removed swipe detection logic
        return False

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")


class LongPressSongItem(OneLineAvatarIconListItem):
    long_press_duration = 1.0
    _lp_event = None
    file_path = StringProperty("")
    font_name = StringProperty(system_font)
    # إضافة خاصية index
    index = NumericProperty(-1)
    # إضافة خاصية is_current_song
    is_current_song = BooleanProperty(False)

    def __init__(self, **kwargs):
        # استخراج index من kwargs إذا كان موجودًا
        index = kwargs.pop('index', -1)
        # استخراج is_current_song من kwargs إذا كان موجودًا
        is_current_song = kwargs.pop('is_current_song', False)

        super(LongPressSongItem, self).__init__(**kwargs)

        # تعيين index بعد استدعاء المُنشئ الأساسي
        self.index = index
        self.is_current_song = is_current_song

        # مؤشر التأثير المرئي
        self._visual_effect = None

        self.bind(font_name=self._apply_font_to_label)
        self._apply_font_to_label(self, self.font_name)

        # إضافة التأثير المرئي إذا كانت هذه هي الأغنية الحالية
        if self.is_current_song:
            Clock.schedule_once(self._add_visual_effect, 0.1)

        # ربط تغييرات خاصية is_current_song
        self.bind(is_current_song=self._on_is_current_song_changed)

    def _apply_font_to_label(self, instance, font_name):
        from kivymd.uix.label import MDLabel
        for child in self.walk():
            if isinstance(child, MDLabel):
                child.font_name = font_name
                # Add support for right-to-left text
                # Check if text contains Arabic characters
                if any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in child.text):
                    child.halign = 'right'  # Right align for Arabic text
                    child.text_language = 'ar'  # Set language to Arabic

    def _add_visual_effect(self, dt):
        """إضافة تأثير دوائر متموجة في الجانب الأيمن من عنصر الأغنية"""
        try:
            # إنشاء التأثير المرئي
            from playing_indicator import PlayingIndicator
            self._visual_effect = PlayingIndicator()

            # تعيين حجم التأثير المرئي - مربع للدوائر المتموجة
            self._visual_effect.size = (dp(40), dp(40))

            # وضع التأثير المرئي في الجانب الأيمن من العنصر
            self._visual_effect.pos = (self.x + self.width - dp(50), self.y + (self.height - dp(40)) / 2)

            # إضافة التأثير المرئي إلى العنصر
            self.add_widget(self._visual_effect)

            # ربط موضع التأثير المرئي بموضع وحجم العنصر
            self.bind(pos=self._update_effect_pos, size=self._update_effect_pos)

            # تحديث الموضع مباشرة
            self._update_effect_pos(self, self.pos)
        except Exception as e:
            print(f"Error adding visual effect: {e}")

    def _update_effect_pos(self, instance, pos):
        """تحديث موضع التأثير المرئي"""
        if self._visual_effect:
            # وضع التأثير المرئي في الجانب الأيمن من العنصر
            self._visual_effect.pos = (self.x + self.width - dp(50), self.y + (self.height - dp(40)) / 2)

    def _on_is_current_song_changed(self, instance, value):
        """معالجة تغييرات خاصية is_current_song"""
        if value:  # إذا أصبحت هذه هي الأغنية الحالية
            Clock.schedule_once(self._add_visual_effect, 0.1)
        else:  # إذا لم تعد هذه هي الأغنية الحالية
            if self._visual_effect:
                self.remove_widget(self._visual_effect)
                self._visual_effect = None

    def on_touch_down(self, touch):
        if self.collide_point(*touch.pos):
            self._lp_event = Clock.schedule_once(self.trigger_long_press, self.long_press_duration)
        return super(LongPressSongItem, self).on_touch_down(touch)

    def on_touch_up(self, touch):
        if self._lp_event:
            self._lp_event.cancel()
            self._lp_event = None
        return super(LongPressSongItem, self).on_touch_up(touch)

    def trigger_long_press(self, dt):
        parent = self.parent
        while parent:
            if hasattr(parent, 'handle_long_press'):
                parent.handle_long_press(self)
                break
            parent = parent.parent

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class AlbumGridItem(ButtonBehavior, AsyncImage):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.size_hint = (None, None)
        self.size = (dp(150), dp(150))
        self.radius = [dp(20), ]
        self.elevation = 2
        self.bind(on_release=self.on_press)

    def on_press(self, *args):
        anim = Animation(scale=0.95, duration=0.1) + Animation(scale=1.0, duration=0.1)
        anim.start(self)

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

class MusicPlayer(BoxLayout):
    playlist = ListProperty()
    favorites = ListProperty()
    current_index = NumericProperty(-1)
    is_playing = BooleanProperty(False)
    sound = ObjectProperty(None)
    seek_scheduled = BooleanProperty(False)
    user_seeking = BooleanProperty(False)
    theme_name = StringProperty("Blue")
    is_search_visible = BooleanProperty(False)
    is_favorites_visible = BooleanProperty(False)
    current_pos = NumericProperty(0)
    shuffle = BooleanProperty(False)
    repeat = BooleanProperty(False)
    _bottom_bar_touch_start = NumericProperty(0)
    current_font = StringProperty('NotoNaskhArabic-VariableFont_wght')
    pending_search_query = StringProperty("")
    volume = NumericProperty(1.0)  # Default volume level (0.0 to 1.0)

    def contains_arabic(self, text):
        """
        Check if the text contains Arabic characters.

        Args:
            text (str): The text to check

        Returns:
            bool: True if the text contains Arabic characters, False otherwise
        """
        if not text or not isinstance(text, str):
            return False

        # Arabic Unicode range (U+0600 to U+06FF)
        return any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in text)

    # Properties for online songs
    is_online_song = BooleanProperty(False)  # Flag to indicate if current song is from online source
    online_song_title = StringProperty("")    # Title of the online song
    online_song_artist = StringProperty("")   # Artist of the online song
    online_song_url = StringProperty("")      # URL of the online song
    online_song_thumbnail = StringProperty("") # Thumbnail URL of the online song

    # Propiedades para el mejorador de audio
    audio_enhancement_enabled = BooleanProperty(True)  # Activar mejora de audio por defecto
    bass_level = NumericProperty(0)  # Nivel de graves (-10 a +10)
    mid_level = NumericProperty(0)   # Nivel de medios (-10 a +10)
    treble_level = NumericProperty(0)  # Nivel de agudos (-10 a +10)
    volume_normalization = BooleanProperty(True)  # Normalización de volumen activada por defecto

    def __init__(self, **kwargs):
        super().__init__(**kwargs)

        # Definir la ruta absoluta a default_album_cover.png
        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images')
        self.default_album_cover = os.path.join(images_dir, 'default_album_cover.png')

        # Programar la actualización de los botones de la barra superior después de que se cargue la interfaz
        Clock.schedule_once(self.update_top_bar_buttons, 0.5)

        # Asegurarse de que la carpeta images existe
        if not os.path.exists(images_dir):
            try:
                os.makedirs(images_dir, exist_ok=True)
                logger.info(f"Carpeta de imágenes creada: {images_dir}")
            except Exception as e:
                logger.error(f"Error al crear carpeta de imágenes: {e}")

        # Verificar si default_album_cover.png existe en la carpeta images
        if not os.path.exists(self.default_album_cover):
            logger.warning(f"La imagen predeterminada no existe en: {self.default_album_cover}")

            # Buscar la imagen en otras ubicaciones
            alt_locations = [
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_album_cover.png'),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers', 'default_album_cover.png')
            ]

            found = False
            for alt_path in alt_locations:
                if os.path.exists(alt_path):
                    # Copiar la imagen a la carpeta images
                    try:
                        import shutil
                        shutil.copy2(alt_path, self.default_album_cover)
                        logger.info(f"Imagen copiada de {alt_path} a {self.default_album_cover}")
                        found = True
                        break
                    except Exception as e:
                        logger.error(f"Error al copiar imagen: {e}")

            # Si no se encontró la imagen, crear una nueva
            if not found:
                try:
                    from PIL import Image
                    img = Image.new('RGB', (300, 300), color=(0, 100, 200))
                    img.save(self.default_album_cover, format='PNG')
                    logger.info(f"Nueva imagen predeterminada creada en: {self.default_album_cover}")
                except Exception as e:
                    logger.error(f"Error al crear imagen predeterminada: {e}")
        else:
            logger.info(f"Imagen predeterminada encontrada: {self.default_album_cover}")

        # Para compatibilidad con código existente
        self.empty_cover = self.default_album_cover

        self.load_theme()
        Clock.schedule_once(self.set_transition, 0)
        # Use a standard interval for progress updates
        Clock.schedule_interval(self.update_progress, 0.1)
        Window.bind(size=self.adjust_layout)
        self._bottom_bar_touch_time = 0
        self._bottom_bar_touch_pos = (0, 0)
        # Flag to track if we're seeking
        self.user_seeking = False
        # إضافة متغير لتخزين مؤقت التقدم
        self.progress_timer = None
        # إضافة متغير لتخزين وقت بدء التشغيل
        self.play_start_time = 0
        # تسجيل شريط التقدم المخصص
        from kivy.factory import Factory
        Factory.register('CustomSlider', cls=CustomSlider)
        # إضافة مؤقت لاستبدال شريط التقدم
        Clock.schedule_once(self.replace_slider, 1)
        # تصغير حجم الشريط السفلي
        Clock.schedule_once(self.resize_bottom_bar, 0.5)

        self.supported_formats = [
            '.mp3', '.wav', '.flac',
            '.m4a', '.mp4', '.aac',
            '.ogg', '.opus',
            '.wma', '.asf',
            '.aiff', '.aif', '.aifc'
        ]
        self.file_manager = MDFileManager(
            exit_manager=self.exit_manager,
            select_path=self.select_path,
            ext=self.supported_formats
        )
        self.playlist = self.load_playlist()
        self.favorites = self.load_favorites()
        self.update_playlist_ui()
        Window.bind(on_request_close=self.on_request_close)

        # Initialize download manager
        self.download_manager = DownloadManager()

        # Set up download screen
        Clock.schedule_once(self.setup_download_screen, 0.5)

        # Inicializar el mejorador de audio
        self.initialize_audio_enhancer()

        # Limpiar portadas corruptas
        Clock.schedule_once(self.clean_corrupted_covers, 2)

        # إعداد معالجات الإشعارات للأندرويد
        self.setup_android_notification_handlers()

        # إعداد الخلفية المحسنة
        Clock.schedule_once(self.setup_enhanced_background, 1.0)

    def initialize_audio_enhancer(self):
        """Inicializa y configura el mejorador de audio"""
        try:
            # Obtener la ruta de FFmpeg
            ffmpeg_path = self.get_ffmpeg_path()

            # Crear instancia del mejorador de audio
            self.audio_enhancer = AudioEnhancer(ffmpeg_path=ffmpeg_path)

            # Configurar el ecualizador con los valores predeterminados
            self.audio_enhancer.set_equalizer(
                bass=self.bass_level,
                mid=self.mid_level,
                treble=self.treble_level
            )

            # Configurar la normalización de volumen
            self.audio_enhancer.set_volume_normalization(self.volume_normalization)

            # Configurar el tamaño del buffer de audio
            self.audio_enhancer.set_audio_buffer_size(8192)

            logger.info("Mejorador de audio inicializado correctamente")
        except Exception as e:
            logger.error(f"Error al inicializar el mejorador de audio: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def setup_download_screen(self, dt):
        """Set up the download screen"""
        try:
            # Find the download screen
            for screen in self.ids.screen_manager.screens:
                if isinstance(screen, DownloadScreen):
                    # Set the download manager
                    screen.download_manager = self.download_manager
                    logger.info("Download screen setup complete")
                    return

            # If we didn't find the screen, create it
            download_screen = DownloadScreen(name='downloads')
            download_screen.download_manager = self.download_manager
            self.ids.screen_manager.add_widget(download_screen)
            logger.info("Download screen created and added")
        except Exception as e:
            logger.error(f"Error setting up download screen: {e}")

    def show_downloads(self):
        """Show the downloads screen"""
        try:
            self.set_screen_transition('slide', 'left')
            self.ids.screen_manager.current = 'downloads'
        except Exception as e:
            logger.error(f"Error showing downloads screen: {e}")

    def show_search_screen(self):
        """Show the online search screen"""
        try:
            # Check if search screen exists
            search_screen = None
            for screen in self.ids.screen_manager.screens:
                if isinstance(screen, SearchScreen):
                    search_screen = screen
                    break

            # If not found, create it
            if not search_screen:
                search_screen = SearchScreen(name='search')
                search_screen.download_manager = self.download_manager
                self.ids.screen_manager.add_widget(search_screen)
                logger.info("Search screen created and added")

            # Show the screen
            self.set_screen_transition('slide', 'left')
            self.ids.screen_manager.current = 'search'
        except Exception as e:
            logger.error(f"Error showing search screen: {e}")

    def open_youtube(self):
        """Open YouTube in the default browser"""
        try:
            import webbrowser
            webbrowser.open("https://www.youtube.com/")
            logger.info("Opening YouTube in browser")
        except Exception as e:
            logger.error(f"Error opening YouTube: {e}")
            # Show error message
            from kivymd.toast import toast
            toast("Could not open YouTube")

    def show_audio_settings(self):
        """Muestra la pantalla de configuración de audio"""
        try:
            self.set_screen_transition('slide', 'left')
            self.ids.screen_manager.current = 'audio_settings'
        except Exception as e:
            logger.error(f"Error mostrando la pantalla de configuración de audio: {e}")

    def show_main_screen(self):
        """Vuelve a la pantalla principal"""
        try:
            self.set_screen_transition('slide', 'right')
            self.ids.screen_manager.current = 'main'
        except Exception as e:
            logger.error(f"Error volviendo a la pantalla principal: {e}")

    def toggle_audio_enhancement(self, value):
        """Activa o desactiva la mejora de audio"""
        try:
            self.audio_enhancement_enabled = value
            logger.info(f"Mejora de audio: {'activada' if value else 'desactivada'}")
        except Exception as e:
            logger.error(f"Error al cambiar la mejora de audio: {e}")

    def toggle_volume_normalization(self, value):
        """Activa o desactiva la normalización de volumen"""
        try:
            self.volume_normalization = value
            if hasattr(self, 'audio_enhancer'):
                self.audio_enhancer.set_volume_normalization(value)
            logger.info(f"Normalización de volumen: {'activada' if value else 'desactivada'}")
        except Exception as e:
            logger.error(f"Error al cambiar la normalización de volumen: {e}")

    def set_bass_level(self, value):
        """Establece el nivel de graves"""
        try:
            self.bass_level = int(value)
            if hasattr(self, 'audio_enhancer'):
                self.audio_enhancer.set_equalizer(bass=self.bass_level)
            if hasattr(self.ids, 'bass_value_label'):
                self.ids.bass_value_label.text = f"{int(value)} dB"
            logger.info(f"Nivel de graves establecido a: {self.bass_level} dB")
        except Exception as e:
            logger.error(f"Error al establecer el nivel de graves: {e}")

    def set_mid_level(self, value):
        """Establece el nivel de medios"""
        try:
            self.mid_level = int(value)
            if hasattr(self, 'audio_enhancer'):
                self.audio_enhancer.set_equalizer(mid=self.mid_level)
            if hasattr(self.ids, 'mid_value_label'):
                self.ids.mid_value_label.text = f"{int(value)} dB"
            logger.info(f"Nivel de medios establecido a: {self.mid_level} dB")
        except Exception as e:
            logger.error(f"Error al establecer el nivel de medios: {e}")

    def set_treble_level(self, value):
        """Establece el nivel de agudos"""
        try:
            self.treble_level = int(value)
            if hasattr(self, 'audio_enhancer'):
                self.audio_enhancer.set_equalizer(treble=self.treble_level)
            if hasattr(self.ids, 'treble_value_label'):
                self.ids.treble_value_label.text = f"{int(value)} dB"
            logger.info(f"Nivel de agudos establecido a: {self.treble_level} dB")
        except Exception as e:
            logger.error(f"Error al establecer el nivel de agudos: {e}")

    def apply_audio_preset(self, preset):
        """Aplica un preset de audio predefinido"""
        try:
            presets = {
                "normal": {"bass": 0, "mid": 0, "treble": 0},
                "bass_boost": {"bass": 6, "mid": 0, "treble": -2},
                "treble_boost": {"bass": -2, "mid": 0, "treble": 6},
                "vocal_boost": {"bass": -2, "mid": 4, "treble": 2}
            }

            if preset in presets:
                settings = presets[preset]
                self.bass_level = settings["bass"]
                self.mid_level = settings["mid"]
                self.treble_level = settings["treble"]

                # Actualizar los sliders
                if hasattr(self.ids, 'bass_slider'):
                    self.ids.bass_slider.value = self.bass_level
                if hasattr(self.ids, 'mid_slider'):
                    self.ids.mid_slider.value = self.mid_level
                if hasattr(self.ids, 'treble_slider'):
                    self.ids.treble_slider.value = self.treble_level

                # Actualizar el ecualizador
                if hasattr(self, 'audio_enhancer'):
                    self.audio_enhancer.set_equalizer(
                        bass=self.bass_level,
                        mid=self.mid_level,
                        treble=self.treble_level
                    )

                logger.info(f"Preset de audio aplicado: {preset}")

                # Mostrar notificación
                try:
                    from kivymd.toast import toast
                    toast(f"Preset aplicado: {preset}")
                except Exception as toast_error:
                    logger.error(f"Error mostrando toast: {toast_error}")
            else:
                logger.warning(f"Preset de audio desconocido: {preset}")
        except Exception as e:
            logger.error(f"Error al aplicar preset de audio: {e}")

    def show_buffer_size_menu(self):
        """Muestra el menú para seleccionar el tamaño del buffer"""
        try:
            buffer_sizes = [
                {"text": "2048 bytes", "size": 2048},
                {"text": "4096 bytes", "size": 4096},
                {"text": "8192 bytes", "size": 8192},
                {"text": "16384 bytes", "size": 16384}
            ]

            menu_items = [
                {
                    "text": item["text"],
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=item["size"]: self.set_buffer_size(x)
                } for item in buffer_sizes
            ]

            self.buffer_menu = MDDropdownMenu(
                caller=self.ids.buffer_size_dropdown,
                items=menu_items,
                width_mult=4
            )
            self.buffer_menu.open()
        except Exception as e:
            logger.error(f"Error mostrando menú de tamaño de buffer: {e}")

    def set_buffer_size(self, size):
        """Establece el tamaño del buffer de audio"""
        try:
            if hasattr(self, 'audio_enhancer'):
                self.audio_enhancer.set_audio_buffer_size(size)

            # Actualizar el texto del dropdown
            if hasattr(self.ids, 'buffer_size_dropdown'):
                self.ids.buffer_size_dropdown.text = f"{size} bytes"

            logger.info(f"Tamaño del buffer de audio establecido a: {size} bytes")

            # Cerrar el menú
            if hasattr(self, 'buffer_menu'):
                self.buffer_menu.dismiss()

            # Mostrar notificación
            try:
                from kivymd.toast import toast
                toast(f"Tamaño del buffer establecido a {size} bytes")
            except Exception as toast_error:
                logger.error(f"Error mostrando toast: {toast_error}")
        except Exception as e:
            logger.error(f"Error al establecer el tamaño del buffer: {e}")

    def create_empty_cover(self):
        """Return the absolute path to the default album cover image"""
        # Buscar la imagen predeterminada en varias ubicaciones posibles
        default_locations = [
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images', 'default_album_cover.png'),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_album_cover.png'),
            os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers', 'default_album_cover.png')
        ]

        # Verificar cada ubicación
        for default_cover in default_locations:
            if os.path.exists(default_cover) and self.is_valid_image(default_cover):
                logger.info(f"استخدام صورة الغلاف الافتراضية الموجودة: {default_cover}")
                return os.path.abspath(default_cover)

        # Si no se encontró la imagen, intentar crearla en la carpeta images
        images_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images')
        default_album_cover = os.path.join(images_dir, 'default_album_cover.png')

        # Crear la carpeta images si no existe
        if not os.path.exists(images_dir):
            try:
                os.makedirs(images_dir, exist_ok=True)
                logger.info(f"تم إنشاء مجلد الصور: {images_dir}")
            except Exception as e:
                logger.error(f"خطأ في إنشاء مجلد الصور: {e}")
                # Intentar usar la carpeta raíz del proyecto
                default_album_cover = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_album_cover.png')

        # Crear una imagen azul simple
        try:
            from PIL import Image
            img = Image.new('RGB', (300, 300), color=(0, 100, 200))
            img.save(default_album_cover, format='PNG')
            logger.info(f"تم إنشاء صورة غلاف افتراضية جديدة: {default_album_cover}")
            return os.path.abspath(default_album_cover)
        except Exception as e:
            logger.error(f"خطأ في إنشاء صورة الغلاف الافتراضية: {e}")

            # Último intento: crear la imagen en la raíz del proyecto
            try:
                blue_cover_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'blue_cover.jpg')
                img = Image.new('RGB', (300, 300), color=(0, 100, 200))
                img.save(blue_cover_path, format='JPEG')
                logger.info(f"تم إنشاء صورة زرقاء بسيطة في المجلد الرئيسي: {blue_cover_path}")
                return os.path.abspath(blue_cover_path)
            except Exception as e2:
                logger.error(f"فشل جميع محاولات إنشاء صورة الغلاف: {e2}")
                return ""

    def get_app_data_dir(self):
        """الحصول على مجلد بيانات التطبيق الآمن للأندرويد"""
        try:
            from android.storage import app_storage_path
            return app_storage_path()
        except Exception as e:
            logger.error(f"Error getting Android storage path: {e}")
            # استخدام المجلد الداخلي للتطبيق كبديل
            try:
                from jnius import autoclass
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                return PythonActivity.getFilesDir().getAbsolutePath()
            except Exception as e2:
                logger.error(f"Error getting Android files dir: {e2}")
                return os.path.dirname(os.path.abspath(__file__))

    def is_valid_image(self, image_path):
        """التحقق مما إذا كانت الصورة صالحة وقابلة للتحميل"""
        try:
            if not image_path or not isinstance(image_path, str):
                logger.warning(f"مسار الصورة غير صالح: {image_path}")
                return False

            if not os.path.exists(image_path):
                logger.warning(f"ملف الصورة غير موجود: {image_path}")
                return False

            # التحقق من حجم الملف (يجب أن يكون أكبر من 100 بايت)
            file_size = os.path.getsize(image_path)
            if file_size < 100:
                logger.warning(f"حجم الصورة صغير جدًا ({file_size} بايت): {image_path}")
                return False

            # التحقق من امتداد الملف
            valid_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
            file_ext = os.path.splitext(image_path)[1].lower()
            if file_ext not in valid_extensions:
                logger.warning(f"امتداد الملف غير صالح ({file_ext}): {image_path}")
                return False

            # محاولة فتح الصورة باستخدام PIL للتحقق من صحتها
            try:
                from PIL import Image
                img = Image.open(image_path)
                # التحقق من صحة الصورة بطريقة أكثر قوة
                img.load()  # محاولة تحميل البيانات الفعلية

                # التحقق من أبعاد الصورة
                width, height = img.size
                if width <= 0 or height <= 0 or width > 10000 or height > 10000:
                    logger.warning(f"أبعاد الصورة غير صالحة ({width}x{height}): {image_path}")
                    return False

                # التحقق من وضع الصورة
                if img.mode not in ['RGB', 'RGBA', 'L', 'P']:
                    logger.warning(f"وضع الصورة غير مدعوم ({img.mode}): {image_path}")
                    # لا نرجع False هنا لأن بعض الصور قد تكون صالحة حتى مع أوضاع غير شائعة

                return True
            except Exception as e:
                logger.warning(f"فشل التحقق من صحة الصورة باستخدام PIL: {image_path}, {e}")
                return False

        except Exception as e:
            logger.error(f"خطأ في التحقق من صحة الصورة: {image_path}, {e}")
            return False

    def repair_corrupted_cover(self, image_path):
        """محاولة إصلاح صورة الغلاف التالفة"""
        try:
            if not image_path or not os.path.exists(image_path):
                logger.warning(f"لا يمكن إصلاح صورة غير موجودة: {image_path}")
                return None

            logger.info(f"محاولة إصلاح صورة الغلاف التالفة: {image_path}")

            # 1. محاولة إصلاح الصورة باستخدام PIL
            try:
                from PIL import Image
                import io

                # قراءة البيانات الخام
                with open(image_path, 'rb') as f:
                    image_data = f.read()

                # البحث عن بداية ونهاية بيانات JPEG
                jpeg_start = b'\xff\xd8'
                jpeg_end = b'\xff\xd9'

                if jpeg_start in image_data and jpeg_end in image_data:
                    # استخراج بيانات JPEG صالحة
                    start_pos = image_data.find(jpeg_start)
                    end_pos = image_data.rfind(jpeg_end) + 2

                    if start_pos < end_pos:
                        valid_jpeg_data = image_data[start_pos:end_pos]

                        # حفظ البيانات المصلحة
                        repaired_path = image_path + '.repaired'
                        with open(repaired_path, 'wb') as f:
                            f.write(valid_jpeg_data)

                        # التحقق من الصورة المصلحة
                        try:
                            img = Image.open(repaired_path)
                            img.verify()

                            # استبدال الصورة الأصلية بالصورة المصلحة
                            os.remove(image_path)
                            os.rename(repaired_path, image_path)

                            logger.info(f"تم إصلاح صورة الغلاف بنجاح: {image_path}")
                            return image_path
                        except Exception as e:
                            logger.warning(f"فشل التحقق من الصورة المصلحة: {e}")
                            if os.path.exists(repaired_path):
                                os.remove(repaired_path)

                # 2. محاولة إنشاء صورة جديدة من البيانات
                try:
                    # إنشاء صورة فارغة بنفس اسم الملف
                    img = Image.new('RGB', (300, 300), color=(0, 100, 200))
                    img.save(image_path, format='JPEG')

                    logger.info(f"تم إنشاء صورة جديدة بدلاً من الصورة التالفة: {image_path}")
                    return image_path
                except Exception as e:
                    logger.warning(f"فشل إنشاء صورة جديدة: {e}")

            except Exception as e:
                logger.warning(f"فشل إصلاح الصورة باستخدام PIL: {e}")

            # إذا فشلت جميع المحاولات، نرجع None
            return None

        except Exception as e:
            logger.error(f"خطأ في محاولة إصلاح صورة الغلاف: {e}")
            return None

    def set_transition(self, dt):
        self.ids.screen_manager.transition = SlideTransition(direction='up')

    def set_screen_transition(self, transition_type='slide', direction='up', duration=0.3):
        transitions = {
            'slide': SlideTransition,
            'fade': FadeTransition,
            'card': CardTransition,
            'swap': SwapTransition
        }
        if transition_type in transitions:
            if transition_type in ['slide', 'card']:
                self.ids.screen_manager.transition = transitions[transition_type](direction=direction, duration=duration)
            else:
                self.ids.screen_manager.transition = transitions[transition_type](duration=duration)

    def resize_bottom_bar(self, dt):
        """تعديل حجم الشريط السفلي"""
        try:
            if hasattr(self.ids, 'bottom_bar'):
                # تعيين ارتفاع الشريط السفلي إلى 80 بدلاً من 50
                self.ids.bottom_bar.height = dp(80)

                # تعديل حجم العناصر الداخلية للشريط السفلي
                if hasattr(self.ids, 'current_track_name'):
                    self.ids.current_track_name.font_style = 'Body1'
                    self.ids.current_track_name.font_size = sp(14)  # زيادة حجم الخط

                # تعديل حجم صورة الغلاف
                if hasattr(self.ids, 'bottom_bar_album_cover'):
                    self.ids.bottom_bar_album_cover.size = (dp(40), dp(40))  # زيادة حجم صورة الغلاف
        except Exception as e:
            logger.error(f"Error in resize_bottom_bar: {e}")

    def adjust_layout(self, instance, size):
        try:
            width, height = size
            if hasattr(self.ids, 'bottom_bar'):
                # استخدام ارتفاع ثابت للشريط السفلي (80)
                self.ids.bottom_bar.height = dp(80)
            if hasattr(self.ids, 'playlist_list'):
                self.ids.playlist_list.spacing = dp(5) if height < 800 else dp(10)
            if height < 800:
                if hasattr(self.ids, 'current_track_name'):
                    self.ids.current_track_name.font_style = 'Body1'
                    self.ids.current_track_name.font_size = sp(12)  # زيادة حجم الخط
            if hasattr(self.ids, 'seek_slider_main'):
                self.ids.seek_slider_main.height = dp(1) if height < 800 else dp(4)
            if hasattr(self.ids, 'bottom_bar_album_cover'):
                self.ids.bottom_bar_album_cover.size = (dp(40), dp(40))  # زيادة حجم صورة الغلاف
        except Exception as e:
            logger.error(f"Error in adjust_layout: {e}")

    def update_playlist_ui(self):
        """تحديث واجهة المستخدم لقائمة التشغيل بطريقة محسنة للأداء"""
        try:
            logger.info("Updating playlist UI")

            # تخزين الحالة الحالية للقائمة
            current_playlist = getattr(self, '_current_playlist_state', None)
            playlist_to_display = self.favorites if self.is_favorites_visible else self.playlist

            logger.info(f"Playlist to display has {len(playlist_to_display)} items")
            logger.info(f"Current index: {self.current_index}")

            # تخزين المؤشر الحالي السابق للمقارنة
            old_current_index = getattr(self, '_last_current_index', -1)

            # التحقق مما إذا كانت القائمة لم تتغير
            if current_playlist is not None:
                is_same_mode = current_playlist[0] == self.is_favorites_visible

                # التحقق من طول القائمة فقط (أسرع من مقارنة المحتوى)
                is_same_length = len(current_playlist[1]) == len(playlist_to_display)

                # إذا تغير المؤشر الحالي فقط، قم بتحديث المسار الحالي فقط
                if is_same_mode and is_same_length and self.current_index != old_current_index:
                    logger.info("Only current index changed, updating only current song")

                    # تحديث المسار الحالي فقط
                    for child in self.ids.playlist_list.children:
                        if hasattr(child, 'index'):
                            # تحديث المسار السابق
                            if child.index == old_current_index:
                                child.is_current_song = False
                                child.text_color = self.get_text_color()

                            # تحديث المسار الحالي
                            if child.index == self.current_index:
                                child.is_current_song = True
                                child.text_color = [0, 1, 0, 1]

                    # تخزين المؤشر الحالي
                    self._last_current_index = self.current_index
                    return

            # إعادة تعيين الحالة الحالية للقائمة لفرض تحديث كامل
            self._current_playlist_state = None

            # تخزين المؤشر الحالي
            self._last_current_index = self.current_index

            # مسح القائمة الحالية
            self.ids.playlist_list.clear_widgets()

            # التحقق من صحة المؤشر الحالي
            if self.current_index >= len(playlist_to_display) or self.current_index < 0:
                self.current_index = -1

            # تحقق من مستوى تحسين الأداء لتحديد حجم الدفعة
            batch_size = 20  # الحجم الافتراضي
            if hasattr(self, 'performance_optimizer') and hasattr(self.performance_optimizer, 'optimization_level'):
                if self.performance_optimizer.optimization_level == 1:  # أداء عالي
                    batch_size = 30
                elif self.performance_optimizer.optimization_level == 3:  # أداء منخفض
                    batch_size = 10

            # إنشاء دالة لإنشاء عناصر القائمة بشكل متزامن
            def create_playlist_items(dt):
                # مؤشر البداية
                start_index = getattr(self, '_playlist_batch_index', 0)
                # مؤشر النهاية
                end_index = min(start_index + batch_size, len(playlist_to_display))

                logger.info(f"Creating playlist items batch: {start_index} to {end_index}")

                # إنشاء عناصر القائمة
                for index in range(start_index, end_index):
                    path = playlist_to_display[index]
                    if not isinstance(path, str) or not path.strip():
                        logger.warning(f"Invalid path at index {index}: {path}")
                        continue

                    # إنشاء دوال الاستدعاء
                    def make_play_callback(idx):
                        return lambda x: self.play_track_by_index(idx)

                    # الحصول على عنوان الأغنية
                    song_title = self.get_song_title(path)

                    # تحديد ما إذا كان هذا هو المسار الحالي
                    is_current_song = (index == self.current_index)

                    # إنشاء عنصر القائمة - استخدام LongPressSongItem لجميع الأغاني
                    item = LongPressSongItem(
                        text=f"{index + 1}. {song_title}",
                        theme_text_color="Custom",
                        file_path=path,
                        on_release=make_play_callback(index),
                        font_name=self.current_font,
                        index=index,
                        is_current_song=is_current_song  # تعيين خاصية is_current_song
                    )

                    # تعيين لون النص
                    if is_current_song:
                        item.text_color = [0, 1, 0, 1]
                    else:
                        item.text_color = self.get_text_color()

                    # إضافة زر إعدادات الأغنية
                    def make_settings_callback(p, btn):
                        return lambda x: self.show_song_settings_menu(btn, p)

                    # إنشاء زر الإعدادات
                    settings_btn = RightContainer(
                        icon="dots-vertical",
                        theme_text_color="Custom",
                        text_color=self.get_text_color()
                    )
                    # تخزين مرجع للزر نفسه لاستخدامه في استدعاء القائمة
                    settings_btn.bind(on_release=make_settings_callback(path, settings_btn))

                    # إضافة الزر إلى العنصر
                    item.add_widget(settings_btn)

                    # إضافة العنصر إلى القائمة
                    self.ids.playlist_list.add_widget(item)

                    # تحميل صورة الغلاف فقط للمسارات المهمة (المسار الحالي والمسارات القريبة منه والمسارات الأولى)
                    # هذا يحسن الأداء بشكل كبير عند التعامل مع قوائم تشغيل كبيرة
                    should_load_cover = False

                    # تحميل صورة الغلاف للمسار الحالي دائمًا
                    if index == self.current_index:
                        should_load_cover = True
                    # تحميل صورة الغلاف للمسارات القريبة من المسار الحالي
                    elif self.current_index >= 0 and abs(index - self.current_index) <= 5:
                        should_load_cover = True
                    # تحميل صورة الغلاف للمسارات الأولى في القائمة
                    elif index < 10:
                        should_load_cover = True

                    if should_load_cover:
                        self._load_cover_for_item(item, path)
                    else:
                        # استخدام صورة الغلاف الافتراضية للمسارات الأخرى
                        self._add_default_cover_to_item(item)



                # تحديث مؤشر البداية للدفعة التالية
                self._playlist_batch_index = end_index

                # إذا لم يتم إنشاء جميع العناصر، جدولة الدفعة التالية
                if end_index < len(playlist_to_display):
                    Clock.schedule_once(create_playlist_items, 0)
                else:
                    logger.info("Finished creating all playlist items")
                    # تخزين الحالة الجديدة للقائمة بعد الانتهاء من إنشاء جميع العناصر
                    self._current_playlist_state = (self.is_favorites_visible, playlist_to_display.copy())

                    # تنظيف الذاكرة المؤقتة للصور إذا كانت كبيرة جدًا
                    if hasattr(self, '_cover_cache') and len(self._cover_cache) > 100:
                        # الاحتفاظ بالصور الأكثر استخدامًا فقط
                        try:
                            # تحديد الصور التي سيتم الاحتفاظ بها
                            keep_paths = []

                            # الاحتفاظ بصور المسارات الحالية
                            for i in range(max(0, self.current_index - 10), min(len(playlist_to_display), self.current_index + 10)):
                                if i >= 0 and i < len(playlist_to_display):
                                    keep_paths.append(playlist_to_display[i])

                            # الاحتفاظ بصور المسارات الأولى
                            for i in range(min(20, len(playlist_to_display))):
                                keep_paths.append(playlist_to_display[i])

                            # إزالة الصور غير المستخدمة
                            keys_to_remove = []
                            for path in self._cover_cache:
                                if path not in keep_paths:
                                    keys_to_remove.append(path)

                            # حذف الصور من الذاكرة المؤقتة
                            for path in keys_to_remove:
                                del self._cover_cache[path]

                            logger.info(f"Cleaned cover cache, removed {len(keys_to_remove)} items")
                        except Exception as cache_error:
                            logger.error(f"Error cleaning cover cache: {cache_error}")

                    # إظهار إشعار إذا تم إضافة أغنية جديدة في بداية القائمة
                    if not self.is_favorites_visible and len(playlist_to_display) > 0 and current_playlist is not None:
                        old_playlist = current_playlist[1] if isinstance(current_playlist, tuple) and len(current_playlist) > 1 else []
                        if len(playlist_to_display) > len(old_playlist) and playlist_to_display[0] not in old_playlist:
                            try:
                                from kivymd.toast import toast
                                song_title = self.get_song_title(playlist_to_display[0])
                                toast(f"Added new song to playlist: {song_title}")
                            except Exception as toast_error:
                                logger.error(f"Error showing toast: {toast_error}")

            # بدء إنشاء عناصر القائمة
            self._playlist_batch_index = 0
            Clock.schedule_once(create_playlist_items, 0)

        except Exception as e:
            logger.error(f"Error updating playlist UI: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def show_delete_confirmation(self, path):
        def delete_track(permanently=False):
            try:
                if path in self.playlist:
                    self.playlist.remove(path)
                    self.save_playlist()
                if path in self.favorites:
                    self.favorites.remove(path)
                    self.save_favorites()
                if permanently:
                    try:
                        os.remove(path)
                    except Exception as e:
                        logger.error(f"Error deleting file: {e}")
                self.update_playlist_ui()
            except Exception as e:
                logger.error(f"Error in delete_track: {e}")
            finally:
                dialog.dismiss()

        dialog = MDDialog(
            title="Confirm Deletion",
            text=f"Do you want to remove '{os.path.basename(path)}'?",
            buttons=[
                MDFlatButton(
                    text="Cancel",
                    theme_text_color="Custom",
                    text_color=self.get_error_color(),
                    on_release=lambda x: dialog.dismiss()
                ),
                MDFlatButton(
                    text="Remove from Playlist",
                    theme_text_color="Custom",
                    text_color=self.get_primary_color(),
                    on_release=lambda x: delete_track(permanently=False)
                ),
                MDFlatButton(
                    text="Delete Permanently",
                    theme_text_color="Custom",
                    text_color=self.get_error_color(),
                    on_release=lambda x: delete_track(permanently=True)
                )
            ]
        )
        dialog.open()

    def get_album_cover(self, path):
        """Obtener la portada de un archivo de audio o devolver la portada predeterminada"""
        try:
            # Verificar que default_album_cover.png existe
            default_cover = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images', 'default_album_cover.png')

            # Si no existe la imagen predeterminada, usar una ruta alternativa
            if not os.path.exists(default_cover):
                alt_paths = [
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_album_cover.png'),
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers', 'default_album_cover.png')
                ]
                for alt_path in alt_paths:
                    if os.path.exists(alt_path):
                        default_cover = alt_path
                        break

            # Actualizar la variable default_album_cover
            self.default_album_cover = default_cover

            # Si el archivo no existe o no es válido, devolver la portada predeterminada
            if not path or not isinstance(path, str) or not os.path.exists(path):
                return os.path.abspath(self.default_album_cover)

            # Inicializar caché si no existe
            if not hasattr(self, '_cover_cache'):
                self._cover_cache = {}

            # Verificar caché
            if path in self._cover_cache:
                cached_path = self._cover_cache[path]
                if os.path.exists(cached_path):
                    return cached_path
                else:
                    # Eliminar entrada inválida
                    del self._cover_cache[path]

            # Crear directorio de portadas si no existe
            covers_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers')
            if not os.path.exists(covers_dir):
                try:
                    os.makedirs(covers_dir, exist_ok=True)
                except Exception:
                    return os.path.abspath(self.default_album_cover)

            # Generar nombre de archivo único
            import hashlib
            file_hash = hashlib.md5(path.encode()).hexdigest()
            cover_path = os.path.join(covers_dir, f"cover_{file_hash}.jpg")

            # Verificar si la portada ya existe
            if os.path.exists(cover_path) and os.path.getsize(cover_path) > 0:
                self._cover_cache[path] = cover_path
                return os.path.abspath(cover_path)

            # Intentar extraer portada del archivo
            try:
                cover_data = self.extract_album_cover(path)
                if cover_data:
                    with open(cover_path, "wb") as cover_file:
                        cover_file.write(cover_data)
                    self._cover_cache[path] = cover_path
                    return os.path.abspath(cover_path)
            except Exception:
                pass

            # Si no se pudo extraer, usar la portada predeterminada
            self._cover_cache[path] = self.default_album_cover
            return os.path.abspath(self.default_album_cover)

        except Exception:
            # En caso de error, siempre devolver la portada predeterminada
            return os.path.abspath(self.default_album_cover)

# تم إزالة دالة search_album_cover_online لتجنب البحث عن صور الأغلفة عبر الإنترنت

    def extract_album_cover(self, path):
        """استخراج بيانات صورة الغلاف من ملف صوتي"""
        if not path or not os.path.exists(path):
            return None

        # طباعة رسالة تأكيد لتتبع استدعاء الدالة
        logger.info(f"Extracting album cover for: {os.path.basename(path)}")

        # الحصول على امتداد الملف
        file_ext = os.path.splitext(path)[1].lower()
        cover_data = None

        # الحصول على اسم الملف بدون المسار
        filename = os.path.basename(path)

        # استخراج بيانات الغلاف حسب نوع الملف
        try:
            # 1. محاولة استخراج الغلاف من الملف نفسه
            if file_ext == '.mp3':
                try:
                    audio = ID3(path)
                    for key in audio.keys():
                        if key.startswith('APIC:'):
                            cover_data = audio[key].data
                            logger.info(f"تم استخراج صورة الغلاف من ملف MP3: {filename}")
                            break
                except Exception as e:
                    logger.debug(f"خطأ في استخراج صورة الغلاف من MP3: {e}")

            elif file_ext == '.flac':
                try:
                    audio = FLAC(path)
                    if audio.pictures:
                        cover_data = audio.pictures[0].data
                        logger.info(f"تم استخراج صورة الغلاف من ملف FLAC: {filename}")
                except Exception as e:
                    logger.debug(f"خطأ في استخراج صورة الغلاف من FLAC: {e}")

            elif file_ext in ['.m4a', '.mp4', '.aac']:
                try:
                    audio = MP4(path)
                    if 'covr' in audio:
                        cover_data = audio['covr'][0]
                        logger.info(f"تم استخراج صورة الغلاف من ملف M4A/MP4: {filename}")
                except Exception as e:
                    logger.debug(f"خطأ في استخراج صورة الغلاف من M4A/MP4: {e}")

            elif file_ext in ['.ogg', '.opus']:
                try:
                    if file_ext == '.ogg':
                        audio = OggVorbis(path)
                    else:
                        audio = OggOpus(path)

                    if 'metadata_block_picture' in audio:
                        import base64
                        import struct
                        picture_data = base64.b64decode(audio['metadata_block_picture'][0])
                        offset = 8
                        mime_length = struct.unpack('>I', picture_data[offset:offset + 4])[0]
                        offset += 4 + mime_length
                        desc_length = struct.unpack('>I', picture_data[offset:offset + 4])[0]
                        offset += 4 + desc_length
                        offset += 16
                        data_length = struct.unpack('>I', picture_data[offset:offset + 4])[0]
                        offset += 4
                        cover_data = picture_data[offset:offset + data_length]
                        logger.info(f"تم استخراج صورة الغلاف من ملف OGG/OPUS: {filename}")
                except Exception as e:
                    logger.debug(f"خطأ في استخراج صورة الغلاف من OGG/OPUS: {e}")

            elif file_ext in ['.wma', '.asf']:
                try:
                    audio = ASF(path)
                    for attr in audio.get('WM/Picture', []):
                        cover_data = attr.value
                        logger.info(f"تم استخراج صورة الغلاف من ملف WMA/ASF: {filename}")
                        break
                except Exception as e:
                    logger.debug(f"خطأ في استخراج صورة الغلاف من WMA/ASF: {e}")

            # 2. إذا لم يتم العثور على صورة الغلاف، ابحث عن ملفات الصور في نفس المجلد
            if not cover_data:
                try:
                    dir_path = os.path.dirname(path)
                    cover_filenames = ['cover.jpg', 'cover.png', 'folder.jpg', 'folder.png',
                                      'album.jpg', 'album.png', 'front.jpg', 'front.png',
                                      'artwork.jpg', 'artwork.png', 'albumart.jpg', 'albumart.png']

                    for cover_file in cover_filenames:
                        cover_path = os.path.join(dir_path, cover_file)
                        if os.path.exists(cover_path):
                            with open(cover_path, 'rb') as f:
                                cover_data = f.read()
                                logger.info(f"تم العثور على ملف صورة الغلاف: {cover_path}")
                                break
                except Exception as e:
                    logger.debug(f"خطأ في البحث عن ملفات صور الغلاف: {e}")

            # Si no se encontró una portada, simplemente devolver None
            if not cover_data:
                logger.info("No se encontró portada en el archivo")

            return cover_data

        except Exception as e:
            logger.error(f"خطأ في استخراج صورة الغلاف: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def show_now_playing(self):
        """عرض شاشة التشغيل الآن"""
        try:
            # Check if we're playing a song (either from playlist or online)
            if self.current_index != -1 or self.is_online_song:
                self.set_screen_transition('card', 'up')
                self.ids.screen_manager.current = 'now_playing'
                self.update_now_playing_ui()
                # استدعاء دالة on_enter_now_playing لتصحيح زر التشغيل/الإيقاف المؤقت
                self.on_enter_now_playing()
        except Exception as e:
            logger.error(f"خطأ في عرض شاشة التشغيل الآن: {e}")

    def show_now_playing_screen(self):
        """عرض شاشة التشغيل الآن عند النقر على الشريط السفلي"""
        try:
            # التحقق من وجود أغنية قيد التشغيل (محلية أو عبر الإنترنت)
            if self.is_playing or self.is_online_song:
                # استخدام الدالة الموجودة لعرض شاشة التشغيل الآن
                self.show_now_playing()
                logger.info("Showing Now Playing screen from bottom bar click")
        except Exception as e:
            logger.error(f"خطأ في show_now_playing_screen: {e}")

    def update_now_playing_ui(self):
        """تحديث واجهة المستخدم لشاشة التشغيل الآن"""
        try:
            # تحقق مما إذا كان التحديث الأخير حدث منذ أقل من 0.5 ثانية (لتجنب التحديثات المتكررة)
            current_time = time.time()
            last_update_time = getattr(self, '_last_now_playing_update_time', 0)

            # للأغاني الأونلاين، نقلل من تكرار التحديثات لتحسين الأداء
            if self.is_online_song and (current_time - last_update_time) < 0.5:
                return

            # تحديث وقت آخر تحديث
            self._last_now_playing_update_time = current_time

            # Check if we're playing a local song from playlist
            if self.current_index != -1 and not self.is_online_song:
                # الحصول على قائمة التشغيل المناسبة والمسار الحالي
                playlist_to_play = self.favorites if self.is_favorites_visible else self.playlist
                track_path = playlist_to_play[self.current_index]

                # تحديث شريط التقدم والوقت أولاً (الأكثر أهمية للمستخدم)
                try:
                    if self.sound:
                        # Set max value first (فقط إذا تغيرت)
                        if self.sound.length > 0:
                            if not hasattr(self.ids.seek_slider_now_playing, 'max') or self.ids.seek_slider_now_playing.max != self.sound.length:
                                self.ids.seek_slider_now_playing.max = self.sound.length

                        # Set current position (فقط إذا تغيرت بشكل ملحوظ)
                        current_pos = self.sound.get_pos()
                        if current_pos >= 0:
                            if not hasattr(self.ids.seek_slider_now_playing, 'value') or abs(self.ids.seek_slider_now_playing.value - current_pos) > 0.5:
                                self.ids.seek_slider_now_playing.value = current_pos

                        # Update time displays (فقط إذا تغيرت)
                        formatted_time = self.format_time(current_pos if current_pos >= 0 else 0)
                        if self.ids.current_time_now_playing.text != formatted_time:
                            self.ids.current_time_now_playing.text = formatted_time

                        formatted_total = self.format_time(self.sound.length)
                        if self.ids.total_time_now_playing.text != formatted_total:
                            self.ids.total_time_now_playing.text = formatted_total
                    else:
                        # تعيين القيم الافتراضية فقط إذا كانت مختلفة عن القيم الحالية
                        if self.ids.seek_slider_now_playing.max != 100:
                            self.ids.seek_slider_now_playing.max = 100
                        if self.ids.seek_slider_now_playing.value != 0:
                            self.ids.seek_slider_now_playing.value = 0
                        if self.ids.current_time_now_playing.text != '00:00':
                            self.ids.current_time_now_playing.text = '00:00'
                        if self.ids.total_time_now_playing.text != '00:00':
                            self.ids.total_time_now_playing.text = '00:00'
                except Exception as ui_error:
                    logger.error(f"Error updating progress bar in UI: {ui_error}")

                # تحديث باقي واجهة المستخدم في خلفية منفصلة (مع تأخير أطول للأغاني الأونلاين)
                def update_ui_background(dt):
                    try:
                        # تحقق مما إذا كان عنوان الأغنية قد تغير
                        current_title = getattr(self, '_current_song_title', '')
                        song_title = self.get_song_title(track_path)

                        # تحديث عنوان الأغنية فقط إذا تغير
                        if current_title != song_title and hasattr(self.ids, 'now_playing_track_name'):
                            self._current_song_title = song_title

                            # تعيين النص والخط
                            self.ids.now_playing_track_name.text = song_title
                            self.ids.now_playing_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                            # التحقق من وجود نص عربي
                            is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in song_title)

                            # ضبط محاذاة النص والخصائص الأخرى
                            if is_arabic:
                                self.ids.now_playing_track_name.halign = 'right'
                                self.ids.now_playing_track_name.text_language = 'ar'
                                self.ids.now_playing_track_name.font_size = sp(18)
                            else:
                                self.ids.now_playing_track_name.halign = 'center'
                                self.ids.now_playing_track_name.font_size = sp(16)

                            # بدء تحريك النص إذا كان طويلاً
                            self.start_song_title_animation()

                        # تحديث صورة الغلاف فقط إذا تغيرت
                        current_cover_path = getattr(self, '_current_cover_path', '')
                        new_cover_path = self.get_album_cover(track_path)

                        if current_cover_path != new_cover_path:
                            self.update_album_cover(track_path)
                            self.start_cover_rotation()

                        # تحديث معلومات الفنان
                        self.extract_artist_info(track_path)

                        # تحديث أزرار التشغيل
                        self.fix_play_button()

                    except Exception as e:
                        logger.error(f"Error updating UI background: {e}")

                # جدولة تحديث واجهة المستخدم في خلفية منفصلة
                from kivy.clock import Clock
                Clock.schedule_once(update_ui_background, 0.2)

            # Check if we're playing an online song
            elif self.is_online_song:
                # تحديث شريط التقدم والوقت أولاً (الأكثر أهمية للمستخدم)
                try:
                    if self.sound:
                        # Set max value first (فقط إذا تغيرت)
                        if self.sound.length > 0:
                            if not hasattr(self.ids.seek_slider_now_playing, 'max') or self.ids.seek_slider_now_playing.max != self.sound.length:
                                self.ids.seek_slider_now_playing.max = self.sound.length

                        # Set current position (فقط إذا تغيرت بشكل ملحوظ)
                        current_pos = self.sound.get_pos()
                        if current_pos >= 0:
                            if not hasattr(self.ids.seek_slider_now_playing, 'value') or abs(self.ids.seek_slider_now_playing.value - current_pos) > 0.5:
                                self.ids.seek_slider_now_playing.value = current_pos

                        # Update time displays (فقط إذا تغيرت)
                        formatted_time = self.format_time(current_pos if current_pos >= 0 else 0)
                        if self.ids.current_time_now_playing.text != formatted_time:
                            self.ids.current_time_now_playing.text = formatted_time

                        formatted_total = self.format_time(self.sound.length)
                        if self.ids.total_time_now_playing.text != formatted_total:
                            self.ids.total_time_now_playing.text = formatted_total
                    else:
                        # تعيين القيم الافتراضية فقط إذا كانت مختلفة عن القيم الحالية
                        if self.ids.seek_slider_now_playing.max != 100:
                            self.ids.seek_slider_now_playing.max = 100
                        if self.ids.seek_slider_now_playing.value != 0:
                            self.ids.seek_slider_now_playing.value = 0
                        if self.ids.current_time_now_playing.text != '00:00':
                            self.ids.current_time_now_playing.text = '00:00'
                        if self.ids.total_time_now_playing.text != '00:00':
                            self.ids.total_time_now_playing.text = '00:00'
                except Exception as ui_error:
                    logger.error(f"Error updating progress bar in UI for online song: {ui_error}")

                # Update the rest of the UI for online song (مع تأخير أطول للأغاني الأونلاين)
                def update_online_ui_background(dt):
                    try:
                        # تحقق مما إذا كان عنوان الأغنية قد تغير
                        current_title = getattr(self, '_current_online_song_title', '')

                        # تحديث عنوان الأغنية فقط إذا تغير
                        if current_title != self.online_song_title and hasattr(self.ids, 'now_playing_track_name'):
                            self._current_online_song_title = self.online_song_title

                            # تعيين النص والخط
                            self.ids.now_playing_track_name.text = self.online_song_title
                            self.ids.now_playing_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                            # التحقق من وجود نص عربي
                            is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in self.online_song_title)

                            # ضبط محاذاة النص والخصائص الأخرى
                            if is_arabic:
                                self.ids.now_playing_track_name.halign = 'right'
                                self.ids.now_playing_track_name.text_language = 'ar'
                                self.ids.now_playing_track_name.font_size = sp(18)
                            else:
                                self.ids.now_playing_track_name.halign = 'center'
                                self.ids.now_playing_track_name.font_size = sp(16)

                            # بدء تحريك النص إذا كان طويلاً
                            self.start_song_title_animation()

                        # تحقق مما إذا كان اسم الفنان قد تغير
                        current_artist = getattr(self, '_current_online_artist', '')

                        # تحديث اسم الفنان فقط إذا تغير
                        if current_artist != self.online_song_artist and hasattr(self.ids, 'now_playing_artist_name'):
                            self._current_online_artist = self.online_song_artist

                            # تعيين النص والخط
                            self.ids.now_playing_artist_name.text = self.online_song_artist
                            self.ids.now_playing_artist_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                            # التحقق من وجود نص عربي
                            is_arabic_artist = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in self.online_song_artist)

                            # ضبط محاذاة النص والخصائص الأخرى
                            if is_arabic_artist:
                                self.ids.now_playing_artist_name.halign = 'right'
                                self.ids.now_playing_artist_name.text_language = 'ar'
                                self.ids.now_playing_artist_name.font_size = sp(16)
                            else:
                                self.ids.now_playing_artist_name.halign = 'center'
                                self.ids.now_playing_artist_name.font_size = sp(14)

                        # تحقق مما إذا كانت صورة الغلاف قد تغيرت
                        current_thumbnail = getattr(self, '_current_online_thumbnail', '')

                        # تحديث صورة الغلاف فقط إذا تغيرت
                        if current_thumbnail != self.online_song_thumbnail and hasattr(self.ids, 'album_cover'):
                            self._current_online_thumbnail = self.online_song_thumbnail

                            # تعيين صورة الغلاف
                            if self.online_song_thumbnail:
                                self.ids.album_cover.source = self.online_song_thumbnail
                            else:
                                # استخدام الصورة الافتراضية
                                self.ids.album_cover.source = self.default_album_cover

                            # إعادة تحميل الصورة
                            self.ids.album_cover.reload()

                            # بدء دوران الغلاف
                            self.start_cover_rotation()

                        # تحديث أزرار التشغيل
                        self.fix_play_button()

                    except Exception as e:
                        logger.error(f"Error updating online UI background: {e}")

                # Schedule UI update with longer delay for online songs
                from kivy.clock import Clock
                Clock.schedule_once(update_online_ui_background, 0.3)

        except Exception as e:
            logger.error(f"Error updating now playing UI: {e}")

    # تم إزالة دالة extract_dominant_color لأننا لم نعد بحاجة إليها

    def update_album_cover(self, path):
        """تحديث صورة الغلاف في واجهة المستخدم بطريقة محسنة للأداء"""
        try:
            # تحقق مما إذا كانت الصورة قد تم تحميلها بالفعل
            current_cover = getattr(self, '_current_cover_path', None)

            # الحصول على مسار صورة الغلاف
            cover_path = self.get_album_cover(path)

            # التحقق من وجود الصورة
            if not cover_path or not os.path.exists(cover_path):
                # إذا لم تكن هناك صورة، استخدم default_album_cover.png
                cover_path = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images', 'default_album_cover.png'))

                # إذا لم تكن موجودة، ابحث في مواقع أخرى
                if not os.path.exists(cover_path):
                    alt_paths = [
                        os.path.join(os.path.dirname(os.path.abspath(__file__)), 'default_album_cover.png'),
                        os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers', 'default_album_cover.png')
                    ]
                    for alt_path in alt_paths:
                        if os.path.exists(alt_path):
                            cover_path = os.path.abspath(alt_path)
                            break

            # إذا كانت نفس الصورة الحالية، لا داعي للتحديث
            if current_cover == cover_path:
                return

            # تحديث المتغير الذي يخزن مسار الصورة الحالية
            self._current_cover_path = cover_path

            # تحقق من حجم الصورة قبل تحميلها
            try:
                if os.path.exists(cover_path):
                    file_size = os.path.getsize(cover_path)
                    # إذا كان حجم الصورة كبيرًا جدًا، قم بتحميلها في خلفية منفصلة
                    if file_size > 500000:  # أكبر من 500 كيلوبايت
                        Clock.schedule_once(lambda dt: self._load_cover_in_background(cover_path), 0.1)
                        return
            except Exception as size_error:
                logger.debug(f"خطأ في التحقق من حجم الصورة: {size_error}")

            # تحديث الصورة في شاشة التشغيل الحالية
            if hasattr(self.ids, 'album_cover'):
                self.ids.album_cover.source = cover_path

                # إعادة تحميل الصورة فقط إذا كانت مختلفة عن الصورة الحالية
                if getattr(self.ids.album_cover, 'source', '') != cover_path:
                    self.ids.album_cover.reload()

                # إعادة تعيين زاوية الدوران
                if hasattr(self.ids.album_cover, 'angle'):
                    self.ids.album_cover.angle = 0

            # تحديث الصورة في الشريط السفلي
            if hasattr(self.ids, 'bottom_bar_album_cover'):
                self.ids.bottom_bar_album_cover.source = cover_path

                # إعادة تحميل الصورة فقط إذا كانت مختلفة عن الصورة الحالية
                if getattr(self.ids.bottom_bar_album_cover, 'source', '') != cover_path:
                    self.ids.bottom_bar_album_cover.reload()

            logger.debug(f"تم تحديث صورة الغلاف: {cover_path}")

        except Exception as e:
            logger.error(f"خطأ في تحديث صورة الغلاف: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # في حالة الخطأ، استخدم لونًا أزرق كخلفية
            try:
                if hasattr(self.ids, 'album_cover'):
                    self.ids.album_cover.source = ""
                    self.ids.album_cover.color = [0.2, 0.6, 1, 1]  # أزرق
                if hasattr(self.ids, 'bottom_bar_album_cover'):
                    self.ids.bottom_bar_album_cover.source = ""
                    self.ids.bottom_bar_album_cover.color = [0.2, 0.6, 1, 1]  # أزرق
            except Exception as e2:
                logger.error(f"خطأ في تعيين لون الخلفية: {e2}")

        # استخدام الدالة الجديدة لاستخراج معلومات الفنان
        self.extract_artist_info(path)

    def _load_cover_in_background(self, cover_path):
        """تحميل صورة الغلاف في خلفية منفصلة لتحسين الأداء"""
        try:
            # تحميل الصورة في خلفية منفصلة
            def load_cover():
                try:
                    from kivy.core.image import Image as CoreImage
                    # تحميل الصورة مسبقًا في الذاكرة المؤقتة
                    img = CoreImage(cover_path)

                    # تحديث واجهة المستخدم في الخيط الرئيسي
                    def update_ui(dt):
                        if hasattr(self.ids, 'album_cover'):
                            self.ids.album_cover.source = cover_path
                            self.ids.album_cover.reload()
                            # إعادة تعيين زاوية الدوران
                            if hasattr(self.ids.album_cover, 'angle'):
                                self.ids.album_cover.angle = 0

                        if hasattr(self.ids, 'bottom_bar_album_cover'):
                            self.ids.bottom_bar_album_cover.source = cover_path
                            self.ids.bottom_bar_album_cover.reload()

                    Clock.schedule_once(update_ui, 0)
                except Exception as e:
                    logger.error(f"خطأ في تحميل الصورة في الخلفية: {e}")

            # تشغيل خيط منفصل لتحميل الصورة
            import threading
            thread = threading.Thread(target=load_cover)
            thread.daemon = True
            thread.start()
        except Exception as e:
            logger.error(f"خطأ في بدء تحميل الصورة في الخلفية: {e}")

    def extract_artist_info(self, path):
        """استخراج معلومات الفنان من الملف الصوتي"""
        try:
            # استخدام متغير مؤقت لتخزين اسم الفنان الحالي
            current_artist = getattr(self, '_current_artist', None)

            # التحقق من وجود الملف
            if not path or not os.path.exists(path):
                logger.warning(f"File does not exist: {path}")
                return

            # الحصول على امتداد الملف
            file_ext = os.path.splitext(path)[1].lower()
            artist_name = "Unknown Artist"

            # استخراج اسم الفنان من الملف حسب نوعه
            if file_ext == '.mp3':
                try:
                    tags = ID3(path)
                    if 'TPE1' in tags:
                        artist_name = str(tags['TPE1'])
                except Exception as mp3_error:
                    logger.debug(f"Error extracting MP3 artist info: {mp3_error}")
            elif file_ext == '.flac':
                try:
                    audio = FLAC(path)
                    if 'artist' in audio:
                        artist_name = audio['artist'][0]
                except Exception as flac_error:
                    logger.debug(f"Error extracting FLAC artist info: {flac_error}")
            elif file_ext in ['.m4a', '.mp4', '.aac']:
                try:
                    audio = MP4(path)
                    if '\xa9ART' in audio:
                        artist_name = audio['\xa9ART'][0]
                except Exception as mp4_error:
                    logger.debug(f"Error extracting MP4 artist info: {mp4_error}")
            elif file_ext in ['.ogg', '.oga', '.opus']:
                try:
                    from mutagen.oggvorbis import OggVorbis
                    audio = OggVorbis(path)
                    if 'artist' in audio:
                        artist_name = audio['artist'][0]
                except Exception as ogg_error:
                    logger.debug(f"Error extracting OGG artist info: {ogg_error}")
            elif file_ext in ['.wav']:
                # WAV files typically don't have metadata
                pass

            # تحديث اسم الفنان فقط إذا كان مختلفًا عن الاسم الحالي
            if hasattr(self.ids, 'now_playing_artist_name'):
                # تخزين اسم الفنان الحالي
                self._current_artist = artist_name

                # تعيين النص
                self.ids.now_playing_artist_name.text = artist_name
                self.ids.now_playing_artist_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                # ضبط محاذاة النص العربي
                is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in artist_name)
                if is_arabic:
                    self.ids.now_playing_artist_name.halign = 'right'
                    self.ids.now_playing_artist_name.text_language = 'ar'
                    self.ids.now_playing_artist_name.font_size = sp(16)
                else:
                    self.ids.now_playing_artist_name.halign = 'center'
                    self.ids.now_playing_artist_name.font_size = sp(14)

            logger.info(f"Artist info extracted: {artist_name}")
            return artist_name

        except Exception as e:
            logger.error(f"Error extracting artist info: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return "Unknown Artist"

    def start_cover_rotation(self):
        """
        Start a continuous rotation animation on the album_cover widget.
        تبدأ دوران صورة الغلاف بطريقة تحسن الأداء
        """
        try:
            # تحقق مما إذا كانت الأغنية الأونلاين، وإذا كانت كذلك، استخدم دوران أبطأ لتحسين الأداء
            is_online = getattr(self, 'is_online_song', False)

            # تحقق مما إذا كان التطبيق في المقدمة أو الخلفية على الأندرويد
            is_foreground = True  # افتراضيًا، نفترض أن التطبيق في المقدمة
            try:
                from jnius import autoclass
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                if PythonActivity.mActivity:
                    is_foreground = PythonActivity.mActivity.hasWindowFocus()
            except:
                pass

            # إذا كان التطبيق في الخلفية، لا تبدأ الدوران لتوفير موارد النظام
            if not is_foreground:
                print("App is in background, not starting cover rotation")
                return

            # تحديد مدة الدوران بناءً على نوع الأغنية وحالة النظام
            # - للأغاني المحلية: 20 ثانية
            # - للأغاني الأونلاين: 30 ثانية
            # - للأجهزة منخفضة الأداء: 40 ثانية
            rotation_duration = 30 if is_online else 20

            # تحقق من أداء الجهاز (إذا كان متاحًا)
            if hasattr(self, 'performance_optimizer') and hasattr(self.performance_optimizer, 'is_low_performance'):
                if self.performance_optimizer.is_low_performance:
                    rotation_duration = 40  # أبطأ للأجهزة منخفضة الأداء

            # تحقق مما إذا كان الدوران قد بدأ بالفعل
            if hasattr(self, '_cover_rotation_active') and self._cover_rotation_active:
                # تجنب إعادة بدء الدوران إذا كان نشطًا بالفعل
                return

            # تحقق من وجود صورة الغلاف
            if not hasattr(self.ids, 'album_cover'):
                print("Album cover widget not found")
                return

            cover = self.ids.album_cover

            # إلغاء أي دوران موجود
            if hasattr(self, '_cover_anim') and self._cover_anim:
                self._cover_anim.cancel(cover)

            # إنشاء دوران جديد
            from kivy.animation import Animation
            # إعادة تعيين الزاوية إلى 0 أولاً
            cover.angle = 0
            # إنشاء دوران بزاوية 360 درجة
            anim = Animation(angle=360, duration=rotation_duration)
            # جعل الدوران مستمرًا
            anim.repeat = True
            # تخزين مرجع للدوران
            self._cover_anim = anim
            # تعيين علامة الدوران النشط
            self._cover_rotation_active = True
            # بدء الدوران
            anim.start(cover)

            # إضافة مراقب لحالة التطبيق (في المقدمة/الخلفية) على أندرويد
            try:
                # إلغاء أي مراقب موجود
                if hasattr(self, '_pause_app_listener'):
                    Window.unbind(on_pause=self._pause_app_listener)
                if hasattr(self, '_resume_app_listener'):
                    Window.unbind(on_resume=self._resume_app_listener)

                # إضافة مراقب جديد
                self._pause_app_listener = lambda *args: self.stop_cover_rotation()
                self._resume_app_listener = lambda *args: self.start_cover_rotation()
                Window.bind(on_pause=self._pause_app_listener)
                Window.bind(on_resume=self._resume_app_listener)
            except Exception as bind_error:
                print(f"Error binding app state listeners: {bind_error}")

            print(f"Album cover rotation started with duration: {rotation_duration}s")
        except Exception as e:
            logger.error(f"Error starting cover rotation: {e}")

    def stop_cover_rotation(self):
        """
        Stop the rotation and reset angle.
        إيقاف دوران صورة الغلاف وإعادة تعيين الزاوية
        """
        try:
            # إلغاء الدوران إذا كان موجودًا
            if hasattr(self, '_cover_anim') and self._cover_anim and hasattr(self.ids, 'album_cover'):
                cover = self.ids.album_cover
                self._cover_anim.cancel(cover)
                cover.angle = 0
                print("Album cover rotation stopped")

            # إعادة تعيين علامة الدوران النشط
            self._cover_rotation_active = False

            # إلغاء مراقبي حالة التطبيق
            if platform == 'android':
                try:
                    if hasattr(self, '_pause_app_listener'):
                        Window.unbind(on_pause=self._pause_app_listener)
                    if hasattr(self, '_resume_app_listener'):
                        Window.unbind(on_resume=self._resume_app_listener)
                except Exception as unbind_error:
                    print(f"Error unbinding app state listeners: {unbind_error}")

        except Exception as e:
            logger.error(f"Error stopping cover rotation: {e}")

    def back_to_main(self):
        try:
            if self.is_favorites_visible:
                self.is_favorites_visible = False
                self.ids.top_app_bar.title = "Music Player"
                # Asegurarse de que los botones de la barra superior sean correctos
                self.update_top_bar_buttons()
                self.update_playlist_ui()
            elif self.ids.screen_manager.current == 'downloads':
                self.set_screen_transition('slide', 'right')
                self.ids.screen_manager.current = 'main'
                # Asegurarse de que los botones de la barra superior sean correctos
                self.update_top_bar_buttons()
            else:
                self.set_screen_transition('card', 'down')
                self.ids.screen_manager.current = 'main'
                # Asegurarse de que los botones de la barra superior sean correctos
                self.update_top_bar_buttons()
                # Stop the album cover rotation when leaving now playing screen
                self.stop_cover_rotation()
        except Exception as e:
            logger.error(f"Error in back_to_main: {e}")

    def exit_manager(self, *args):
        try:
            self.file_manager.close()
        except Exception as e:
            logger.error(f"Error closing file manager: {e}")

    def show_file_chooser(self):
        try:
            self.file_manager.selector = 'file'
            start_path = os.path.dirname(os.path.abspath(__file__)) if platform == 'android' else os.path.expanduser("~")
            self.file_manager.show(start_path)
        except Exception as e:
            logger.error(f"Error showing file chooser: {e}")

    def show_folder_chooser(self):
        try:
            self.file_manager.selector = 'folder'
            start_path = os.path.dirname(os.path.abspath(__file__)) if platform == 'android' else os.path.expanduser("~")
            self.file_manager.show(start_path)
        except Exception as e:
            logger.error(f"Error showing folder chooser: {e}")

    def select_path(self, path):
        try:
            self.exit_manager()
            if os.path.isdir(path):
                self.add_folder_to_playlist(path)
            elif os.path.isfile(path):
                self.add_to_playlist([path])
        except Exception as e:
            logger.error(f"Error selecting path: {e}")

    def add_to_playlist(self, selection):
        try:
            if selection:
                for path in selection:
                    if path not in self.playlist and os.path.exists(path):
                        self.playlist.append(path)
                self.save_playlist()
                self.update_playlist_ui()
        except Exception as e:
            logger.error(f"Error adding to playlist: {e}")

    def add_to_playlist_at_beginning(self, selection):
        """Add tracks to the beginning of the playlist"""
        try:
            logger.info(f"add_to_playlist_at_beginning called with: {selection}")

            if not selection:
                logger.warning("Selection is empty, nothing to add")
                return

            # Log current playlist state
            logger.info(f"Current playlist has {len(self.playlist)} tracks")

            # Normalize paths and verify each path in the selection
            normalized_selection = []
            for path in selection:
                # Normalize path (handle different path separators)
                norm_path = os.path.normpath(path)

                # Check if file exists
                if not os.path.isfile(norm_path):
                    logger.warning(f"Path does not exist or is not a file: {norm_path}")

                    # Try to find the file with a similar name in the same directory
                    dir_name = os.path.dirname(norm_path)
                    base_name = os.path.basename(norm_path)

                    if os.path.exists(dir_name):
                        logger.info(f"Looking for similar files in {dir_name}")
                        files = os.listdir(dir_name)

                        # Try to find a file with a similar name
                        for file in files:
                            if file.lower() == base_name.lower() or base_name.lower() in file.lower():
                                potential_match = os.path.join(dir_name, file)
                                if os.path.isfile(potential_match):
                                    logger.info(f"Found potential match: {potential_match}")
                                    norm_path = potential_match
                                    break

                # Check again if the file exists (after potential match)
                if os.path.isfile(norm_path):
                    logger.info(f"Path exists: {norm_path}")
                    normalized_selection.append(norm_path)
                else:
                    logger.error(f"Cannot add non-existent file: {norm_path}")

            # Add files in reverse order to maintain the original order at the beginning
            added_count = 0
            for path in reversed(normalized_selection):
                # Double check file existence
                if os.path.isfile(path):
                    if path not in self.playlist:
                        logger.info(f"Adding new track to beginning: {path}")
                        self.playlist.insert(0, path)
                        added_count += 1
                    elif path in self.playlist:
                        # If the file is already in the playlist, remove it and add it to the beginning
                        logger.info(f"Moving existing track to beginning: {path}")
                        self.playlist.remove(path)
                        self.playlist.insert(0, path)
                        added_count += 1
                else:
                    logger.error(f"File disappeared before adding to playlist: {path}")

            if added_count > 0:
                logger.info(f"Saving playlist with {len(self.playlist)} tracks")
                self.save_playlist()

                # Reset the playlist UI state to force a complete refresh
                if hasattr(self, '_current_playlist_state'):
                    logger.info("Resetting playlist UI state to force refresh")
                    self._current_playlist_state = None

                # Update the UI
                logger.info("Updating playlist UI")
                self.update_playlist_ui()

                # Show a notification to the user
                try:
                    from kivymd.toast import toast
                    toast(f"Added {added_count} tracks to the beginning of the playlist")
                except Exception as toast_error:
                    logger.error(f"Error showing toast: {toast_error}")

                # Log the addition
                logger.info(f"Added {added_count} tracks to the beginning of the playlist")

                # Don't automatically play the first track after download
                # if not self.is_playing and added_count > 0 and len(self.playlist) > 0:
                #     logger.info("No track currently playing, attempting to play the first added track")
                #     Clock.schedule_once(lambda dt: self.play_track_by_index(0), 1.0)
                logger.info("Track added to playlist but not auto-playing (as per user preference)")
            else:
                logger.warning("No tracks were added to the playlist")

        except Exception as e:
            logger.error(f"Error adding to beginning of playlist: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def add_folder_to_playlist(self, folder_path):
        try:
            if not os.path.exists(folder_path):
                return

            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in self.supported_formats):
                        file_path = os.path.join(root, file)
                        if file_path not in self.playlist and os.path.exists(file_path):
                            self.playlist.append(file_path)
            self.save_playlist()
            self.update_playlist_ui()
        except Exception as e:
            logger.error(f"Error adding folder to playlist: {e}")

    def save_playlist(self):
        try:
            # Skip saving if no write permission on Android
            if platform == 'android' and not check_permission(Permission.WRITE_EXTERNAL_STORAGE):
                logger.warning("No write permission—skipping save_playlist")
                return

            path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "playlist.json")
            with open(path, "w", encoding="utf-8") as file:
                json.dump(self.playlist, file)
        except Exception as e:
            logger.error(f"Error saving playlist: {e}")

    def load_playlist(self):
        try:
            playlist_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "playlist.json")
            if os.path.exists(playlist_path):
                with open(playlist_path, "r") as file:
                    loaded_playlist = json.load(file)
                    # Filter out non-existent files
                    return [path for path in loaded_playlist if isinstance(path, str) and os.path.exists(path)]
            return []
        except Exception as e:
            logger.error(f"Error loading playlist: {e}")
            return []

    def save_favorites(self):
        try:
            # Skip saving if no write permission on Android
            if platform == 'android' and not check_permission(Permission.WRITE_EXTERNAL_STORAGE):
                logger.warning("No write permission—skipping save_favorites")
                return

            path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "favorites.json")
            with open(path, "w", encoding="utf-8") as file:
                json.dump(self.favorites, file)
        except Exception as e:
            logger.error(f"Error saving favorites: {e}")

    def load_favorites(self):
        try:
            favorites_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "favorites.json")
            if os.path.exists(favorites_path):
                with open(favorites_path, "r") as file:
                    loaded_favorites = json.load(file)
                    # Filter out non-existent files
                    return [path for path in loaded_favorites if isinstance(path, str) and os.path.exists(path)]
            return []
        except Exception as e:
            logger.error(f"Error loading favorites: {e}")
            return []

    def toggle_favorite(self, path):
        try:
            if path in self.favorites:
                self.favorites.remove(path)
            else:
                self.favorites.append(path)
            self.save_favorites()
            self.update_playlist_ui()
        except Exception as e:
            logger.error(f"Error toggling favorite: {e}")

    def reset_online_song(self):
        """Reset online song properties when playing a local song"""
        print("DEBUG: Reset online song properties")
        self.is_online_song = False
        self.online_song_title = ""
        self.online_song_artist = ""
        self.online_song_url = ""
        self.online_song_thumbnail = ""
        if hasattr(self, 'online_song_video_id'):
            self.online_song_video_id = ""

        # تنظيف متغيرات الإيقاف المؤقت المتعلقة بالأغاني الأونلاين
        if hasattr(self, '_paused_online_title'):
            self._paused_online_title = ""
        if hasattr(self, '_paused_online_artist'):
            self._paused_online_artist = ""
        if hasattr(self, '_paused_online_thumbnail'):
            self._paused_online_thumbnail = ""
        if hasattr(self, '_paused_online_video_id'):
            self._paused_online_video_id = ""
        if hasattr(self, '_current_video_id'):
            self._current_video_id = None

        # تنظيف متغيرات الإيقاف المؤقت الأخرى
        if hasattr(self, '_paused_sound_path') and self._paused_sound_path and ('youtube.com' in self._paused_sound_path or 'youtu.be' in self._paused_sound_path or 'googlevideo.com' in self._paused_sound_path):
            print("DEBUG: Clearing paused online sound path")
            self._paused_sound_path = None

        logger.debug("Reset online song properties")

    def play_track_by_index(self, index):
        """تشغيل مسار بواسطة مؤشره في قائمة التشغيل، دائمًا من البداية"""
        try:
            # Reset online song properties when playing a local song
            self.reset_online_song()

            # الحصول على قائمة التشغيل المناسبة
            playlist = self.favorites if self.is_favorites_visible else self.playlist
            if not playlist:
                logger.debug("Playlist is empty")
                return False

            # التحقق من صحة المؤشر
            if not (0 <= index < len(playlist)):
                logger.debug(f"Invalid index: {index}, playlist length: {len(playlist)}")
                return False

            # تعيين المؤشر الجديد
            self.current_index = index

            # الحصول على مسار الملف
            path = playlist[index]

            # Normalize path
            path = os.path.normpath(path)

            # Check if file exists
            if not os.path.isfile(path):
                logger.debug(f"File does not exist: {path}")

                # Try to find the file with a similar name in the same directory
                dir_name = os.path.dirname(path)
                base_name = os.path.basename(path)

                if os.path.exists(dir_name):
                    logger.info(f"Looking for similar files in {dir_name}")
                    files = os.listdir(dir_name)

                    # Try to find a file with a similar name
                    for file in files:
                        if file.lower() == base_name.lower() or base_name.lower() in file.lower():
                            potential_match = os.path.join(dir_name, file)
                            if os.path.isfile(potential_match):
                                logger.info(f"Found potential match: {potential_match}")
                                path = potential_match

                                # Update the playlist entry with the correct path
                                playlist[index] = path
                                self.save_playlist()
                                break

                # Check again if the file exists (after potential match)
                if not os.path.isfile(path):
                    logger.error(f"Cannot play non-existent file: {path}")

                    # Try to play the next track if available
                    if len(playlist) > index + 1:
                        logger.info(f"Trying to play next track at index {index + 1}")
                        return self.play_track_by_index(index + 1)
                    return False

            # إيقاف مؤقت التقدم أولاً
            self.stop_progress_timer()

            # إيقاف الصوت الحالي إذا كان موجودًا
            if hasattr(self, 'sound') and self.sound:
                try:
                    self.sound.stop()
                    self.sound = None
                except Exception:
                    pass

            # إعادة تعيين متغيرات الإيقاف المؤقت
            self._paused_position = 0
            self._paused_sound_path = None

            # إعادة تعيين موضع التشغيل
            self.current_pos = 0
            self.play_start_time = time.time()

            # تحميل الملف الصوتي باستخدام SoundLoader
            try:
                from kivy.core.audio import SoundLoader

                # Log file details before loading
                file_size = os.path.getsize(path)
                logger.info(f"Loading sound file: {path} (Size: {file_size} bytes)")

                # Check if file is empty
                if file_size == 0:
                    logger.error(f"Cannot play empty file: {path}")

                    # Try to play the next track if available
                    if len(playlist) > index + 1:
                        logger.info(f"Trying to play next track at index {index + 1}")
                        return self.play_track_by_index(index + 1)
                    return False

                new_sound = SoundLoader.load(path)

                if not new_sound:
                    logger.error(f"Failed to load sound: {path}")

                    # Try to play the next track if available
                    if len(playlist) > index + 1:
                        logger.info(f"Trying to play next track at index {index + 1}")
                        return self.play_track_by_index(index + 1)
                    return False

                # تعيين كائن الصوت الجديد فقط بعد التأكد من تحميله بنجاح
                self.sound = new_sound
                logger.info(f"Sound loaded successfully: {path}")

                # ضبط مستوى الصوت
                self.sound.volume = self.volume

                # تشغيل الصوت
                self.sound.play()
                logger.info(f"Sound playing: {path}")

                # تعيين حالة التشغيل
                self.is_playing = True
            except Exception as load_error:
                logger.error(f"Error loading or playing sound: {load_error}")

                # Try to play the next track if available
                if len(playlist) > index + 1:
                    logger.info(f"Trying to play next track at index {index + 1}")
                    return self.play_track_by_index(index + 1)
                return False

            # تحديث واجهة المستخدم - استخدام Clock.schedule_once لتحسين الأداء
            def update_ui(dt):
                try:
                    song_title = self.get_song_title(path)
                    logger.info(f"Updating UI with song title: {song_title}")

                    # تحديث اسم المسار في الشريط السفلي فقط
                    if hasattr(self.ids, 'current_track_name'):
                        self.ids.current_track_name.text = song_title
                        self.ids.current_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                        # تعيين محاذاة النص للنص العربي
                        is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in song_title)
                        if is_arabic:
                            self.ids.current_track_name.halign = 'right'
                            self.ids.current_track_name.text_language = 'ar'
                            # ضبط حجم الخط للنص العربي
                            self.ids.current_track_name.font_size = sp(16)
                        else:
                            # ضبط حجم الخط للنص الإنجليزي
                            self.ids.current_track_name.font_size = sp(14)

                    # تحديث أزرار التشغيل
                    self.fix_play_button()

                    # تحديث وقت الأغنية في الشريط السفلي
                    if hasattr(self.ids, 'current_time_main'):
                        self.ids.current_time_main.text = self.format_time(0)
                    if hasattr(self.ids, 'total_time_main') and hasattr(self.sound, 'length'):
                        self.ids.total_time_main.text = self.format_time(self.sound.length)

                    # بدء مؤقت التقدم
                    self.start_progress_timer()

                    # تحديث واجهة المستخدم
                    self.update_ui_play_state()

                    # تحديث البيانات الوصفية وصورة الغلاف في خلفية منفصلة
                    def update_metadata_and_cover(dt):
                        # تحديث البيانات الوصفية
                        self.update_metadata(path)

                        # تحديث صورة الغلاف
                        self.update_album_cover(path)

                        # تحديث شاشة التشغيل الآن إذا كانت مفتوحة
                        if self.ids.screen_manager.current == 'now_playing':
                            self.update_now_playing_ui()
                        else:
                            # تحديث اسم المسار في الشريط السفلي فقط
                            if hasattr(self.ids, 'current_track_name'):
                                self.ids.current_track_name.text = song_title
                                self.ids.current_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                                # تعيين محاذاة النص للنص العربي
                                if is_arabic:
                                    self.ids.current_track_name.halign = 'right'
                                    self.ids.current_track_name.text_language = 'ar'
                                    self.ids.current_track_name.font_size = sp(16)
                                else:
                                    self.ids.current_track_name.font_size = sp(14)

                    # جدولة تحديث البيانات الوصفية وصورة الغلاف بعد تأخير قصير
                    Clock.schedule_once(update_metadata_and_cover, 0.1)

                except Exception as ui_error:
                    logger.error(f"Error updating UI: {ui_error}")
                    import traceback
                    logger.error(traceback.format_exc())

            # تحديث قائمة التشغيل فورًا
            self.update_playlist_ui()

            # جدولة تحديث واجهة المستخدم في الإطار التالي
            Clock.schedule_once(update_ui, 0)

            # تحديث الخلفية بناءً على الأغنية الجديدة
            Clock.schedule_once(lambda dt: self.update_background_theme(), 0.5)

            return True

        except Exception as e:
            logger.error(f"Error playing track by index: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def play_youtube(self, url, title="", artist="", thumbnail_url=""):
        """تشغيل فيديو من يوتيوب"""
        print(f"Attempting to play YouTube video: {url}")
        logger.info(f"Attempting to play YouTube video: {url}")

        if not url:
            print("No YouTube URL provided")
            return False

        # Check if we have YouTube libraries available
        try:
            # Try to import YouTube libraries
            youtube_libs_available = False

            try:
                import yt_dlp
                youtube_libs_available = True
                print("Using yt-dlp for YouTube playback")
            except ImportError:
                try:
                    from pytube import YouTube
                    youtube_libs_available = True
                    print("Using pytube for YouTube playback")
                except ImportError:
                    youtube_libs_available = False

            if not youtube_libs_available:
                print("No YouTube libraries available")
                # Try to play as regular URL
                return self.play_url(url, title, artist, thumbnail_url)

            # استخراج معرف الفيديو من الرابط
            video_id = self._extract_youtube_video_id(url)
            if video_id:
                print(f"Extracted video ID: {video_id}")
                # تخزين معرف الفيديو للاستخدام لاحقًا
                self._current_video_id = video_id

                # Validar y actualizar la URL de la miniatura si es necesario
                if not thumbnail_url or thumbnail_url == "":
                    # Si no se proporcionó una miniatura, generar una a partir del ID de video
                    thumbnail_url = self._get_valid_thumbnail_url(video_id)
                    print(f"Generated thumbnail URL: {thumbnail_url}")
                elif "UCMIdeeBjp_60Jv7ROpRxK6Q" in thumbnail_url:
                    # Si la miniatura contiene un ID incorrecto, reemplazarla
                    print(f"Detected invalid thumbnail URL: {thumbnail_url}")
                    thumbnail_url = self._get_valid_thumbnail_url(video_id)
                    print(f"Replaced with valid thumbnail URL: {thumbnail_url}")
            else:
                print("Could not extract video ID from URL")
                self._current_video_id = None

            # Extract audio URL from YouTube video
            audio_url = self._extract_youtube_audio_url(url)

            if not audio_url:
                print("Failed to extract audio URL from YouTube video")
                # Try to play as regular URL
                return self.play_url(url, title, artist, thumbnail_url)

            # Instead of streaming directly, download to a temporary file first
            print(f"Downloading YouTube audio to temporary file: {audio_url}")

            # Use play_url instead of play_url_streaming for YouTube URLs
            # play_url will download the file to a temporary location first
            return self.play_url(audio_url, title, artist, thumbnail_url)

        except Exception as e:
            print(f"Error in play_youtube: {e}")
            logger.error(f"Error in play_youtube: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # Try to play as regular URL
            return self.play_url(url, title, artist, thumbnail_url)

    def play_url_streaming(self, url, title="", artist="", thumbnail_url="", resume_position=None):
        """تشغيل ملف صوتي من رابط URL بطريقة البث المباشر"""
        print(f"Attempting to stream URL: {url}")
        logger.info(f"Attempting to stream URL: {url}")

        if not url:
            print("No URL provided")
            return False

        try:
            # حفظ الموضع الحالي إذا كنا نستأنف نفس الأغنية
            saved_position = None
            if resume_position is not None:
                saved_position = resume_position
                print(f"Will resume playback at position: {saved_position}")
            elif hasattr(self, 'is_online_song') and self.is_online_song and title and self.online_song_title == title:
                # إذا كنا نستأنف نفس الأغنية الأونلاين، نحفظ الموضع الحالي
                if hasattr(self, 'current_pos') and self.current_pos > 0:
                    saved_position = self.current_pos
                    print(f"Saved current position for resuming same online song: {saved_position}")

            # إيقاف الصوت الحالي إذا كان موجودًا
            if hasattr(self, 'sound') and self.sound:
                try:
                    self.sound.stop()
                    self.sound = None
                except Exception as e:
                    print(f"Error stopping current sound: {e}")

            # إعادة تعيين متغيرات الإيقاف المؤقت إذا كنا لا نستأنف نفس الأغنية
            if not saved_position:
                if hasattr(self, '_paused_position'):
                    self._paused_position = 0
                if hasattr(self, '_paused_sound_path'):
                    self._paused_sound_path = None

            # تحقق مما إذا كان الرابط من YouTube أو googlevideo
            is_youtube_url = 'youtube.com' in url or 'youtu.be' in url
            is_googlevideo_url = 'googlevideo.com' in url

            # استخراج معرف الفيديو إذا كان الرابط من YouTube
            video_id = None
            if is_youtube_url:
                video_id = self._extract_youtube_video_id(url)
                print(f"Extracted video ID from YouTube URL: {video_id}")
            elif is_googlevideo_url and title:
                # محاولة استخراج معرف الفيديو من العنوان إذا كان متاحًا
                import re
                match = re.search(r'\[([a-zA-Z0-9_-]{11})\]$', title)
                if match:
                    video_id = match.group(1)
                    print(f"Found video ID in title: {video_id}")

            # تخزين معرف الفيديو للاستخدام لاحقًا
            if video_id:
                self.online_song_video_id = video_id
                print(f"Stored video ID: {video_id}")

            # إعادة تعيين موضع التشغيل
            self.current_pos = 0
            self.play_start_time = time.time()

            # Set online song properties
            self.is_online_song = True
            self.online_song_title = title
            self.online_song_artist = artist
            self.online_song_url = url
            self.online_song_thumbnail = thumbnail_url if thumbnail_url else ""

            # تخزين معرف الفيديو الأصلي إذا كان متاحًا
            if hasattr(self, '_current_video_id') and self._current_video_id:
                self.online_song_video_id = self._current_video_id
                print(f"Stored video ID: {self._current_video_id}")
            else:
                # محاولة استخراج معرف الفيديو من الرابط
                video_id = self._extract_youtube_video_id(url)
                if video_id:
                    self.online_song_video_id = video_id
                    print(f"Extracted and stored video ID: {video_id}")
                else:
                    self.online_song_video_id = ""
                    print("No video ID available")

            # تحميل الصوت من الرابط مباشرة
            from kivy.core.audio import SoundLoader
            print(f"Loading sound directly from URL: {url}")

            # تحميل الرابط مباشرة
            try:
                # تحقق مما إذا كان الرابط من YouTube
                is_youtube_url = 'youtube.com' in url or 'youtu.be' in url or 'googlevideo.com' in url

                # إذا كان الرابط من YouTube، نحاول إعادة استخراج رابط جديد أولاً
                if is_youtube_url and hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                    print("URL appears to be from YouTube, trying to refresh it first")
                    fresh_url = self.refresh_youtube_url(self.online_song_video_id)
                    if fresh_url:
                        url = fresh_url
                        print(f"Using freshly extracted URL: {url}")

                # محاولة تحميل الصوت
                self.sound = SoundLoader.load(url)

                if not self.sound:
                    print(f"Failed to load sound from URL: {url}")

                    # إذا كان الرابط من YouTube، نحاول إعادة استخراج رابط جديد
                    if is_youtube_url:
                        print("Trying to extract a fresh YouTube URL")
                        video_id = None

                        # استخدام معرف الفيديو المخزن إذا كان متاحًا
                        if hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                            video_id = self.online_song_video_id
                            print(f"Using stored video ID: {video_id}")
                        else:
                            # محاولة استخراج معرف الفيديو من الرابط
                            video_id = self._extract_youtube_video_id(url)
                            print(f"Extracted video ID from URL: {video_id}")

                        if video_id:
                            # إعادة استخراج رابط جديد
                            fresh_url = self.refresh_youtube_url(video_id)
                            if fresh_url and fresh_url != url:
                                print(f"Trying with fresh URL: {fresh_url}")
                                self.sound = SoundLoader.load(fresh_url)
                                if self.sound:
                                    print("Successfully loaded sound with fresh URL")
                                    url = fresh_url
                                    self.online_song_url = fresh_url

                    # إذا لا يزال الصوت غير متاح، نحاول طريقة التنزيل
                    if not self.sound:
                        print("Still failed to load sound, trying download method")
                        return self.play_url(url, title, artist, thumbnail_url)
            except Exception as load_error:
                print(f"Error loading sound from URL: {load_error}")

                # محاولة إعادة استخراج رابط جديد كخطة بديلة
                if hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                    try:
                        print("Trying to extract a fresh URL as fallback")
                        fresh_url = self.refresh_youtube_url(self.online_song_video_id)
                        if fresh_url:
                            print(f"Trying with fresh fallback URL: {fresh_url}")
                            self.sound = SoundLoader.load(fresh_url)
                            if self.sound:
                                print("Successfully loaded sound with fresh fallback URL")
                                url = fresh_url
                                self.online_song_url = fresh_url
                    except Exception as fallback_error:
                        print(f"Error in fallback URL extraction: {fallback_error}")

                # إذا لا يزال الصوت غير متاح، نحاول طريقة التنزيل
                if not hasattr(self, 'sound') or not self.sound:
                    print("Trying download method as last resort")
                    return self.play_url(url, title, artist, thumbnail_url)

                # إظهار رسالة خطأ للمستخدم إذا فشلت كل المحاولات
                if not hasattr(self, 'sound') or not self.sound:
                    self.show_error_dialog("حدث خطأ أثناء تحميل الأغنية. يرجى المحاولة مرة أخرى.")
                    return False

            print(f"Sound loaded successfully from URL: {url}")

            # ضبط مستوى الصوت
            self.sound.volume = self.volume

            # تشغيل الصوت
            self.sound.play()
            print("Sound play() called")

            # الانتقال إلى الموضع المحفوظ إذا كان متاحًا
            if saved_position and saved_position > 0:
                try:
                    print(f"Seeking to saved position: {saved_position}")
                    # تحديث الموضع الحالي والوقت المنقضي
                    self.current_pos = saved_position
                    self.play_start_time = time.time() - saved_position

                    # تأخير قصير قبل الانتقال للتأكد من أن الصوت جاهز
                    def delayed_seek(dt):
                        try:
                            # التحقق مما إذا كان الصوت لا يزال موجودًا
                            if hasattr(self, 'sound') and self.sound and hasattr(self.sound, 'seek'):
                                # محاولة الانتقال إلى الموضع المحفوظ
                                self.sound.seek(saved_position)
                                print(f"Delayed seek successful")

                                # تحديث واجهة المستخدم لتعكس الموضع الحالي
                                if hasattr(self.ids, 'seek_slider_now_playing'):
                                    self.ids.seek_slider_now_playing.value = saved_position
                                if hasattr(self.ids, 'current_time_now_playing'):
                                    self.ids.current_time_now_playing.text = self.format_time(saved_position)
                            else:
                                print("Sound object not available for seeking")
                        except Exception as delayed_seek_error:
                            print(f"Error in delayed seek: {delayed_seek_error}")
                            # إذا فشل الانتقال، نحاول طريقة بديلة
                            try:
                                # استخدام طريقة المحاكاة الجديدة
                                if hasattr(self, 'simulate_seek_with_restart'):
                                    success = self.simulate_seek_with_restart(saved_position)
                                    if success:
                                        print("Used simulate_seek_with_restart successfully")
                                    else:
                                        # إذا فشلت المحاكاة، نحاول الطريقة القديمة
                                        if hasattr(self, 'sound') and self.sound:
                                            self.sound.stop()
                                            self.sound.play()
                                            # تحديث وقت بدء التشغيل ليعكس الموضع المحفوظ
                                            self.play_start_time = time.time() - saved_position
                                            print("Restarted sound with adjusted play_start_time")
                                else:
                                    # إذا لم تكن طريقة المحاكاة متاحة، نستخدم الطريقة القديمة
                                    if hasattr(self, 'sound') and self.sound:
                                        self.sound.stop()
                                        self.sound.play()
                                        # تحديث وقت بدء التشغيل ليعكس الموضع المحفوظ
                                        self.play_start_time = time.time() - saved_position
                                        print("Restarted sound with adjusted play_start_time")
                            except Exception as restart_error:
                                print(f"Error restarting sound: {restart_error}")

                    # جدولة الانتقال بعد 0.5 ثانية (زيادة التأخير للتأكد من جاهزية الصوت)
                    from kivy.clock import Clock
                    Clock.schedule_once(delayed_seek, 0.5)

                    # جدولة محاولة ثانية بعد 1 ثانية للتأكد
                    Clock.schedule_once(delayed_seek, 1.0)
                except Exception as seek_error:
                    print(f"Error scheduling seek: {seek_error}")

            # تعيين حالة التشغيل
            self.is_playing = True

            # تحديث واجهة المستخدم
            try:
                if hasattr(self.ids, 'current_track_name'):
                    self.ids.current_track_name.text = title
                if hasattr(self.ids, 'now_playing_title'):
                    self.ids.now_playing_title.text = title
                if hasattr(self.ids, 'now_playing_artist'):
                    self.ids.now_playing_artist.text = artist

                # Make bottom bar visible
                if hasattr(self.ids, 'bottom_bar'):
                    self.ids.bottom_bar.opacity = 1
                    self.ids.bottom_bar.disabled = False
            except Exception as ui_error:
                print(f"Error updating UI: {ui_error}")

            # بدء مؤقت التقدم
            try:
                if hasattr(self, 'start_progress_timer'):
                    self.start_progress_timer()
            except Exception as timer_error:
                print(f"Error starting progress timer: {timer_error}")

            # تحديث أزرار التشغيل
            try:
                if hasattr(self, 'fix_play_button'):
                    self.fix_play_button()
            except Exception as button_error:
                print(f"Error fixing play button: {button_error}")

            return True

        except Exception as e:
            print(f"Error streaming URL: {e}")
            logger.error(f"Error streaming URL: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # محاولة استخدام طريقة التنزيل كخطة بديلة
            return self.play_url(url, title, artist, thumbnail_url)

    def _is_valid_youtube_id(self, video_id):
        """Verificar si un ID de YouTube es válido"""
        if not video_id:
            return False

        # Verificar que el ID tenga exactamente 11 caracteres (formato estándar de YouTube)
        if len(video_id) != 11:
            return False

        # Verificar que el ID solo contenga caracteres válidos
        import re
        if not re.match(r'^[a-zA-Z0-9_-]{11}$', video_id):
            return False

        return True

    def _get_valid_thumbnail_url(self, video_id, default_image="images/default_album_cover.png"):
        """Obtener una URL de miniatura válida para un ID de YouTube"""
        if not self._is_valid_youtube_id(video_id):
            print(f"Invalid YouTube video ID for thumbnail: {video_id}")
            return default_image

        # Crear la URL de la miniatura
        thumbnail_url = f"https://img.youtube.com/vi/{video_id}/mqdefault.jpg"

        # Verificar si la URL es accesible (opcional, puede ralentizar la aplicación)
        try:
            import requests
            response = requests.head(thumbnail_url, timeout=2)
            if response.status_code != 200:
                print(f"Thumbnail URL returned status code {response.status_code}: {thumbnail_url}")
                return default_image
        except Exception as e:
            print(f"Error checking thumbnail URL: {e}")
            # Si hay un error, seguimos usando la URL generada

        return thumbnail_url

    def _extract_youtube_video_id(self, url):
        """استخراج معرف فيديو YouTube من الرابط"""
        print(f"Extracting video ID from URL: {url}")

        # Si la URL es None o vacía, retornar None
        if not url:
            print("URL is None or empty")
            return None

        try:
            import re

            # أنماط مختلفة لروابط YouTube
            patterns = [
                r'(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]{11})',  # روابط قياسية
                r'youtube\.com\/embed\/([a-zA-Z0-9_-]{11})',                    # روابط مضمنة
                r'youtube\.com\/v\/([a-zA-Z0-9_-]{11})',                        # روابط قديمة
                r'youtube\.com\/shorts\/([a-zA-Z0-9_-]{11})'                    # روابط shorts
            ]

            # البحث عن معرف الفيديو باستخدام الأنماط
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    video_id = match.group(1)
                    # Verificar que el ID sea válido
                    if self._is_valid_youtube_id(video_id):
                        print(f"Found valid video ID: {video_id}")
                        return video_id
                    else:
                        print(f"Found invalid video ID: {video_id}")

            # Si no se encontró un ID válido con los patrones anteriores, intentar extraer de la URL completa
            # Buscar cualquier segmento de 11 caracteres que parezca un ID de YouTube
            match = re.search(r'[a-zA-Z0-9_-]{11}', url)
            if match:
                potential_id = match.group(0)
                if self._is_valid_youtube_id(potential_id):
                    print(f"Found potential video ID from URL: {potential_id}")
                    return potential_id

            print("Could not extract video ID from URL")
            return None
        except Exception as e:
            print(f"Error extracting video ID: {e}")
            import traceback
            print(traceback.format_exc())
            return None

    def refresh_youtube_url(self, video_id):
        """تحديث رابط الصوت من YouTube عندما تنتهي صلاحية الرابط الحالي"""
        if not video_id:
            print("No video ID provided for URL refresh")
            return None

        # Verificar que el ID de YouTube sea válido
        if not self._is_valid_youtube_id(video_id):
            print(f"Invalid YouTube video ID: {video_id}")
            return None

        try:
            print(f"Refreshing YouTube URL for video ID: {video_id}")
            # إعادة بناء رابط YouTube
            youtube_url = f"https://www.youtube.com/watch?v={video_id}"

            # محاولة استخراج رابط الصوت باستخدام yt-dlp أولاً
            try:
                # استخراج رابط الصوت الجديد
                new_audio_url = self._extract_youtube_audio_url(youtube_url)

                if new_audio_url:
                    print(f"Successfully refreshed YouTube URL: {new_audio_url}")
                    # تحديث الرابط المخزن
                    self.online_song_url = new_audio_url
                    return new_audio_url
            except Exception as extract_error:
                print(f"Error in primary extraction method: {extract_error}")

            # إذا فشلت الطريقة الأولى، نحاول طريقة بديلة
            try:
                import yt_dlp

                # تكوين خيارات yt-dlp مع محاولة الحصول على تنسيق أصغر وأكثر توافقًا
                ydl_opts = {
                    'format': 'bestaudio/best',
                    'quiet': True,
                    'no_warnings': True,
                    'noplaylist': True,
                    'extract_flat': False,
                    'skip_download': True,
                }

                # استخراج معلومات الفيديو
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    print(f"Using alternative extraction method for: {youtube_url}")
                    info = ydl.extract_info(youtube_url, download=False)

                    if info:
                        # الحصول على رابط الصوت
                        if 'url' in info:
                            new_audio_url = info['url']
                            print(f"Successfully refreshed YouTube URL (alternative method): {new_audio_url}")
                            # تحديث الرابط المخزن
                            self.online_song_url = new_audio_url
                            return new_audio_url

                        # إذا لم يتم العثور على رابط مباشر، نبحث في التنسيقات
                        if 'formats' in info:
                            # البحث عن تنسيق صوتي
                            for format in info['formats']:
                                if format.get('acodec') != 'none' and format.get('vcodec') == 'none':
                                    new_audio_url = format['url']
                                    print(f"Found audio-only format: {format.get('format_id')}")
                                    # تحديث الرابط المخزن
                                    self.online_song_url = new_audio_url
                                    return new_audio_url
            except Exception as alt_error:
                print(f"Error in alternative extraction method: {alt_error}")

            # إذا فشلت كل المحاولات، نحاول استخدام pytube
            try:
                from pytube import YouTube

                # استخراج معلومات الفيديو
                yt = YouTube(youtube_url)
                # الحصول على تدفقات الصوت فقط
                audio_streams = yt.streams.filter(only_audio=True)

                if audio_streams:
                    # الحصول على أول تدفق صوتي
                    stream = audio_streams.first()
                    new_audio_url = stream.url
                    print(f"Successfully refreshed YouTube URL (pytube method): {new_audio_url}")
                    # تحديث الرابط المخزن
                    self.online_song_url = new_audio_url
                    return new_audio_url
            except Exception as pytube_error:
                print(f"Error in pytube extraction method: {pytube_error}")

            print("All URL refresh methods failed")
            return None

        except Exception as e:
            print(f"Error refreshing YouTube URL: {e}")
            import traceback
            print(traceback.format_exc())
            return None

    def download_and_play_youtube(self, video_id, title="", artist="", thumbnail_url="", resume_position=None):
        """تنزيل وتشغيل أغنية من YouTube كخطة بديلة عندما يفشل البث المباشر"""
        if not video_id:
            print("No video ID provided for download")
            return False

        # Verificar que el ID de YouTube sea válido
        if not self._is_valid_youtube_id(video_id):
            print(f"Invalid YouTube video ID: {video_id}")
            return False

        # Validar y actualizar la URL de la miniatura si es necesario
        if not thumbnail_url or thumbnail_url == "":
            # Si no se proporcionó una miniatura, generar una a partir del ID de video
            thumbnail_url = self._get_valid_thumbnail_url(video_id)
            print(f"Generated thumbnail URL: {thumbnail_url}")
        elif "UCMIdeeBjp_60Jv7ROpRxK6Q" in thumbnail_url:
            # Si la miniatura contiene un ID incorrecto, reemplazarla
            print(f"Detected invalid thumbnail URL: {thumbnail_url}")
            thumbnail_url = self._get_valid_thumbnail_url(video_id)
            print(f"Replaced with valid thumbnail URL: {thumbnail_url}")

        try:
            print(f"Downloading YouTube audio as fallback for video ID: {video_id}")
            # إعادة بناء رابط YouTube
            youtube_url = f"https://www.youtube.com/watch?v={video_id}"

            # تنزيل الصوت باستخدام yt-dlp
            try:
                import yt_dlp
                import tempfile
                import os
                import time

                # إنشاء ملف مؤقت مع اسم فريد لتجنب التداخل
                temp_dir = tempfile.gettempdir()
                unique_id = f"{video_id}_{int(time.time())}"
                temp_file = os.path.join(temp_dir, f"{unique_id}.mp3")

                # تكوين خيارات yt-dlp للحصول على أصغر حجم ممكن للأداء الأفضل
                ydl_opts = {
                    'format': 'worstaudio/worst',  # استخدام أقل جودة للتحميل الأسرع
                    'outtmpl': temp_file,
                    'quiet': True,
                    'no_warnings': True,
                    'noplaylist': True,
                    'postprocessors': [{
                        'key': 'FFmpegExtractAudio',
                        'preferredcodec': 'mp3',
                        'preferredquality': '96',  # جودة منخفضة للتحميل الأسرع
                    }],
                    'max_filesize': '10M',  # تحديد الحجم الأقصى للملف
                }

                # عرض رسالة للمستخدم
                self.show_info_dialog("جاري تحميل الأغنية... يرجى الانتظار")

                # تنزيل الصوت
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    print(f"Downloading audio to: {temp_file}")
                    ydl.download([youtube_url])

                # التحقق من وجود الملف
                if os.path.exists(temp_file):
                    print(f"Successfully downloaded audio to: {temp_file}")

                    # تشغيل الملف المحلي
                    from kivy.core.audio import SoundLoader

                    # إيقاف الصوت الحالي إذا كان موجودًا
                    if hasattr(self, 'sound') and self.sound:
                        try:
                            self.sound.stop()
                            self.sound = None
                        except Exception as stop_error:
                            print(f"Error stopping current sound: {stop_error}")

                    # تحميل الصوت الجديد
                    self.sound = SoundLoader.load(temp_file)

                    if self.sound:
                        print("Successfully loaded downloaded audio")

                        # تعيين خصائص الأغنية الأونلاين
                        self.is_online_song = True
                        self.online_song_title = title
                        self.online_song_artist = artist
                        self.online_song_thumbnail = thumbnail_url
                        self.online_song_url = youtube_url
                        self.online_song_video_id = video_id

                        # حفظ مسار الملف المؤقت للتنظيف لاحقًا
                        self._temp_audio_file = temp_file

                        # تشغيل الصوت
                        self.sound.volume = self.volume
                        self.sound.play()

                        # تعيين حالة التشغيل
                        self.is_playing = True
                        self.play_start_time = time.time()
                        self.current_pos = 0

                        # الانتقال إلى الموضع المحفوظ إذا كان متاحًا
                        if resume_position and resume_position > 0 and hasattr(self.sound, 'seek'):
                            try:
                                print(f"Seeking to position: {resume_position}")
                                # تأخير قصير قبل الانتقال للتأكد من أن الصوت جاهز
                                def delayed_seek(dt):
                                    try:
                                        self.sound.seek(resume_position)
                                        print(f"Delayed seek successful in downloaded file")
                                        self.current_pos = resume_position
                                        self.play_start_time = time.time() - resume_position
                                    except Exception as delayed_seek_error:
                                        print(f"Error in delayed seek: {delayed_seek_error}")

                                # جدولة الانتقال بعد 0.5 ثانية
                                from kivy.clock import Clock
                                Clock.schedule_once(delayed_seek, 0.5)
                            except Exception as seek_error:
                                print(f"Error scheduling seek in downloaded file: {seek_error}")

                        # تحديث واجهة المستخدم
                        if hasattr(self.ids, 'current_track_name'):
                            self.ids.current_track_name.text = title

                        # بدء مؤقت التقدم
                        self.start_progress_timer()

                        # تحديث أزرار التشغيل
                        self.fix_play_button()

                        return True
                    else:
                        print(f"Failed to load downloaded audio: {temp_file}")
                        # محاولة حذف الملف المؤقت إذا فشل التحميل
                        try:
                            os.remove(temp_file)
                        except:
                            pass
                else:
                    print(f"Downloaded file not found: {temp_file}")
            except Exception as download_error:
                print(f"Error downloading YouTube audio: {download_error}")
                import traceback
                print(traceback.format_exc())

                # محاولة استخدام طريقة بديلة للتنزيل
                try:
                    # استخدام pytube كخطة بديلة
                    from pytube import YouTube
                    import tempfile
                    import os

                    # إنشاء ملف مؤقت
                    temp_dir = tempfile.gettempdir()
                    unique_id = f"{video_id}_{int(time.time())}"
                    temp_file = os.path.join(temp_dir, f"{unique_id}.mp3")

                    # تنزيل الصوت
                    yt = YouTube(youtube_url)
                    audio_stream = yt.streams.filter(only_audio=True).first()

                    if audio_stream:
                        print(f"Downloading audio with pytube to: {temp_file}")
                        audio_stream.download(output_path=temp_dir, filename=f"{unique_id}.mp3")

                        # التحقق من وجود الملف
                        if os.path.exists(temp_file):
                            print(f"Successfully downloaded audio with pytube to: {temp_file}")

                            # تشغيل الملف المحلي
                            from kivy.core.audio import SoundLoader
                            self.sound = SoundLoader.load(temp_file)

                            if self.sound:
                                print("Successfully loaded downloaded audio with pytube")

                                # تعيين خصائص الأغنية الأونلاين
                                self.is_online_song = True
                                self.online_song_title = title
                                self.online_song_artist = artist
                                self.online_song_thumbnail = thumbnail_url
                                self.online_song_url = youtube_url
                                self.online_song_video_id = video_id

                                # حفظ مسار الملف المؤقت للتنظيف لاحقًا
                                self._temp_audio_file = temp_file

                                # تشغيل الصوت
                                self.sound.volume = self.volume
                                self.sound.play()

                                # تعيين حالة التشغيل
                                self.is_playing = True
                                self.play_start_time = time.time()

                                # الانتقال إلى الموضع المحفوظ إذا كان متاحًا
                                if resume_position and resume_position > 0 and hasattr(self.sound, 'seek'):
                                    try:
                                        print(f"Seeking to position: {resume_position}")
                                        self.sound.seek(resume_position)
                                        self.current_pos = resume_position
                                        self.play_start_time = time.time() - resume_position
                                    except Exception as seek_error:
                                        print(f"Error seeking in downloaded file: {seek_error}")

                                # تحديث واجهة المستخدم
                                if hasattr(self.ids, 'current_track_name'):
                                    self.ids.current_track_name.text = title

                                # بدء مؤقت التقدم
                                self.start_progress_timer()

                                # تحديث أزرار التشغيل
                                self.fix_play_button()

                                return True
                except Exception as pytube_error:
                    print(f"Error downloading with pytube: {pytube_error}")

            # إذا وصلنا إلى هنا، فقد فشلت جميع المحاولات
            self.show_error_dialog("فشل تحميل الأغنية. يرجى المحاولة مرة أخرى.")
            return False
        except Exception as e:
            print(f"Error in download_and_play_youtube: {e}")
            import traceback
            print(traceback.format_exc())
            self.show_error_dialog("حدث خطأ أثناء تحميل الأغنية. يرجى المحاولة مرة أخرى.")
            return False

    def simulate_seek_with_restart(self, position):
        """محاكاة الانتقال إلى موضع محدد عن طريق إعادة تشغيل الصوت وضبط وقت البدء"""
        if not hasattr(self, 'sound') or not self.sound:
            print("No sound object available for simulated seek")
            return False

        try:
            print(f"Simulating seek to position {position} by restarting playback")

            # حفظ حالة التشغيل الحالية
            was_playing = self.is_playing

            # إيقاف الصوت الحالي
            self.sound.stop()

            # إعادة تشغيل الصوت
            self.sound.play()

            # ضبط وقت البدء ليعكس الموضع المطلوب
            self.play_start_time = time.time() - position

            # تحديث الموضع الحالي
            self.current_pos = position

            # تحديث واجهة المستخدم
            if hasattr(self.ids, 'seek_slider_now_playing'):
                self.ids.seek_slider_now_playing.value = position
            if hasattr(self.ids, 'current_time_now_playing'):
                self.ids.current_time_now_playing.text = self.format_time(position)

            print(f"Simulated seek successful, adjusted play_start_time to {self.play_start_time}")
            return True
        except Exception as e:
            print(f"Error in simulated seek: {e}")
            return False

    def _extract_youtube_audio_url(self, youtube_url):
        """استخراج رابط الصوت من فيديو يوتيوب"""
        try:
            # Try yt-dlp first
            try:
                import yt_dlp

                # Configure yt-dlp options optimized for streaming and performance
                ydl_opts = {
                    'quiet': True,
                    'no_warnings': True,
                    # Prioritize smaller, faster formats for better performance
                    # Order: small mp3 > small m4a > small webm > any audio
                    'format': 'worstaudio[ext=mp3]/worstaudio[ext=m4a]/worstaudio[ext=webm]/worstaudio/bestaudio[filesize<5M]/bestaudio[filesize<10M]/bestaudio',
                    'noplaylist': True,
                    'skip_download': True,
                    'extract_flat': False,
                    'youtube_include_dash_manifest': False,  # Faster extraction
                    'socket_timeout': 10,  # Faster timeout
                    'retries': 2,  # Fewer retries for faster response
                }

                # Extract audio URL
                with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                    print(f"Extracting audio URL from YouTube: {youtube_url}")
                    info = ydl.extract_info(youtube_url, download=False)

                    if info:
                        # Get the URL of the best audio format
                        if 'url' in info:
                            print("Direct URL found in info")
                            return info['url']

                        # If direct URL not found, try formats
                        if 'formats' in info:
                            # Filter and sort formats by size and quality
                            audio_formats = []

                            for format in info['formats']:
                                if format.get('acodec') != 'none' and (format.get('vcodec') == 'none' or format.get('vcodec') is None):
                                    # Add filesize info for debugging
                                    filesize = format.get('filesize')
                                    if filesize:
                                        filesize_mb = filesize / (1024 * 1024)
                                        print(f"Format {format.get('format_id')}: {format.get('ext')} - {filesize_mb:.2f} MB")
                                    audio_formats.append(format)

                            # Sort formats by filesize (smaller first) for faster loading
                            audio_formats.sort(key=lambda x: x.get('filesize', float('inf')))

                            # Prioritize smaller formats for faster loading
                            if audio_formats:
                                # First try to find a small mp3 format (best compatibility)
                                for fmt in audio_formats:
                                    if fmt.get('ext') == 'mp3' and fmt.get('filesize', float('inf')) < 10 * 1024 * 1024:  # < 10MB
                                        print(f"Found small mp3 format: {fmt.get('format_id')}")
                                        return fmt.get('url')

                                # Then try small m4a
                                for fmt in audio_formats:
                                    if fmt.get('ext') == 'm4a' and fmt.get('filesize', float('inf')) < 10 * 1024 * 1024:  # < 10MB
                                        print(f"Found small m4a format: {fmt.get('format_id')}")
                                        return fmt.get('url')

                                # Then try small webm
                                for fmt in audio_formats:
                                    if fmt.get('ext') == 'webm' and fmt.get('filesize', float('inf')) < 10 * 1024 * 1024:  # < 10MB
                                        print(f"Found small webm format: {fmt.get('format_id')}")
                                        return fmt.get('url')

                                # If no small format found, use the smallest available
                                if audio_formats:
                                    print(f"Using smallest available format: {audio_formats[0].get('format_id')}")
                                    return audio_formats[0].get('url')

                            # If no audio-only format, use the first format with audio
                            for format in info['formats']:
                                if format.get('acodec') != 'none':
                                    print(f"Using format with audio: {format.get('format_id')}")
                                    return format.get('url')

                print("No suitable audio format found")
                return None

            except ImportError:
                # Try pytube if yt-dlp is not available
                try:
                    from pytube import YouTube

                    # Create YouTube object
                    print(f"Using pytube to extract audio URL from: {youtube_url}")
                    yt = YouTube(youtube_url)

                    # Get the audio streams - prioritize smaller formats for better performance
                    audio_streams = yt.streams.filter(only_audio=True)

                    # Sort by file size (ascending) if possible
                    try:
                        sorted_streams = sorted(audio_streams, key=lambda x: x.filesize)
                    except:
                        sorted_streams = audio_streams

                    # Try to get mp3 format first (best compatibility)
                    mp3_stream = next((s for s in sorted_streams if 'mp3' in s.mime_type), None)
                    if mp3_stream:
                        print("Found mp3 audio stream with pytube")
                        return mp3_stream.url

                    # Then try m4a (good compatibility and usually smaller)
                    m4a_stream = next((s for s in sorted_streams if 'mp4' in s.mime_type), None)
                    if m4a_stream:
                        print("Found m4a audio stream with pytube")
                        return m4a_stream.url

                    # Then try webm
                    webm_stream = next((s for s in sorted_streams if 'webm' in s.mime_type), None)
                    if webm_stream:
                        print("Found webm audio stream with pytube")
                        return webm_stream.url

                    # Finally, use any audio format
                    if sorted_streams:
                        print("Using first available audio stream with pytube")
                        return sorted_streams[0].url

                    print("No audio streams found with pytube")
                    return None

                except ImportError:
                    print("Neither yt-dlp nor pytube is available")
                    return None
                except Exception as pytube_error:
                    print(f"Error extracting audio URL with pytube: {pytube_error}")
                    return None

        except Exception as e:
            print(f"Error extracting YouTube audio URL: {e}")
            logger.error(f"Error extracting YouTube audio URL: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    def play_url(self, url, title="", artist="", thumbnail_url=""):
        """تشغيل ملف صوتي من رابط URL"""
        print(f"Attempting to play URL: {url}")
        logger.info(f"Attempting to play URL: {url}")

        if not url:
            print("No URL provided")
            return False

        try:
            # إيقاف الصوت الحالي إذا كان موجودًا
            if hasattr(self, 'sound') and self.sound:
                try:
                    self.sound.stop()
                    self.sound = None
                except Exception as e:
                    print(f"Error stopping current sound: {e}")

            # إعادة تعيين متغيرات الإيقاف المؤقت
            if hasattr(self, '_paused_position'):
                self._paused_position = 0
            if hasattr(self, '_paused_sound_path'):
                self._paused_sound_path = None

            # إعادة تعيين موضع التشغيل
            self.current_pos = 0
            self.play_start_time = time.time()

            # Set online song properties
            self.is_online_song = True
            self.online_song_title = title
            self.online_song_artist = artist
            self.online_song_url = url
            self.online_song_thumbnail = thumbnail_url if thumbnail_url else ""

            # تخزين معرف الفيديو الأصلي إذا كان متاحًا
            if hasattr(self, '_current_video_id') and self._current_video_id:
                self.online_song_video_id = self._current_video_id
                print(f"Stored video ID: {self._current_video_id}")
            else:
                # محاولة استخراج معرف الفيديو من الرابط
                video_id = self._extract_youtube_video_id(url)
                if video_id:
                    self.online_song_video_id = video_id
                    print(f"Extracted and stored video ID: {video_id}")
                else:
                    self.online_song_video_id = ""
                    print("No video ID available")

            # تحميل الصوت من الرابط
            from kivy.core.audio import SoundLoader
            print(f"Loading sound from URL: {url}")

            # تحقق من نوع الرابط
            if url.startswith(('http://', 'https://')):
                print("URL is a web URL")
                # إذا كان الرابط من الإنترنت، قم بتنزيله أولاً
                try:
                    import tempfile
                    import requests
                    from concurrent.futures import ThreadPoolExecutor
                    import threading

                    # إنشاء ملف مؤقت
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.mp3')
                    temp_path = temp_file.name
                    temp_file.close()

                    print(f"Downloading to temporary file: {temp_path}")

                    # تنزيل الملف بطريقة أكثر كفاءة
                    # استخدام حجم قطع أكبر لتسريع التنزيل
                    chunk_size = 32768  # 32KB chunks instead of 8KB

                    # تحقق مما إذا كان الرابط من YouTube
                    is_youtube = 'youtube' in url or 'googlevideo.com' in url

                    # إذا كان الرابط من YouTube، استخدم طريقة تنزيل أكثر كفاءة
                    if is_youtube:
                        # استخدام طريقة تنزيل تدريجي للسماح بالتشغيل أثناء التنزيل
                        response = requests.get(url, stream=True, headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                            'Range': 'bytes=0-'  # طلب البايتات من البداية
                        })

                        # تنزيل جزء صغير من الملف أولاً للسماح ببدء التشغيل بسرعة
                        initial_chunk_size = 1024 * 1024  # 1MB للبداية
                        downloaded_size = 0

                        with open(temp_path, 'wb') as f:
                            # تنزيل الجزء الأول بسرعة
                            for chunk in response.iter_content(chunk_size=chunk_size):
                                if chunk:
                                    f.write(chunk)
                                    downloaded_size += len(chunk)
                                    if downloaded_size >= initial_chunk_size:
                                        break

                        # تحميل الصوت بعد تنزيل الجزء الأول
                        self.sound = SoundLoader.load(temp_path)

                        # إذا تم تحميل الصوت بنجاح، ابدأ التشغيل وأكمل التنزيل في الخلفية
                        if self.sound:
                            # تشغيل الصوت
                            self.sound.volume = self.volume
                            self.sound.play()
                            print("Sound play() called (early start)")
                            self.is_playing = True

                            # إكمال التنزيل في الخلفية
                            def continue_download():
                                try:
                                    with open(temp_path, 'ab') as f:
                                        for chunk in response.iter_content(chunk_size=chunk_size):
                                            if chunk:
                                                f.write(chunk)
                                    print("Background download completed")
                                except Exception as e:
                                    print(f"Error in background download: {e}")

                            # بدء عملية التنزيل في الخلفية
                            download_thread = threading.Thread(target=continue_download)
                            download_thread.daemon = True
                            download_thread.start()
                        else:
                            # إذا فشل التحميل المبكر، قم بتنزيل الملف بالكامل
                            with open(temp_path, 'wb') as f:
                                for chunk in response.iter_content(chunk_size=chunk_size):
                                    if chunk:
                                        f.write(chunk)

                            print(f"Download complete, loading from: {temp_path}")
                            self.sound = SoundLoader.load(temp_path)
                    else:
                        # للروابط غير YouTube، استخدم طريقة التنزيل العادية
                        response = requests.get(url, stream=True)
                        with open(temp_path, 'wb') as f:
                            for chunk in response.iter_content(chunk_size=chunk_size):
                                if chunk:
                                    f.write(chunk)

                        print(f"Download complete, loading from: {temp_path}")
                        self.sound = SoundLoader.load(temp_path)

                    # حفظ مسار الملف المؤقت للتنظيف لاحقًا
                    self._temp_audio_file = temp_path

                except Exception as download_error:
                    print(f"Error downloading audio: {download_error}")
                    # محاولة تحميل الرابط مباشرة كخطة بديلة
                    try:
                        self.sound = SoundLoader.load(url)
                    except Exception as load_error:
                        print(f"Error loading sound from URL: {load_error}")
                        # إظهار رسالة خطأ للمستخدم
                        self.show_error_dialog("حدث خطأ أثناء تحميل الأغنية. يرجى المحاولة مرة أخرى.")
                        return False
            else:
                # إذا كان الرابط محليًا، قم بتحميله مباشرة
                print("URL is a local path")
                try:
                    self.sound = SoundLoader.load(url)
                except Exception as load_error:
                    print(f"Error loading sound from local path: {load_error}")
                    return False

            if not self.sound:
                print(f"Failed to load sound from URL: {url}")
                # محاولة تشغيل الصوت باستخدام طريقة play_track إذا كان الرابط محليًا
                if not url.startswith(('http://', 'https://')) and os.path.exists(url):
                    print(f"Trying to play as local file using play_track")
                    return self.play_track(url)
                # إذا كان الرابط أونلاين، حاول إعادة استخراج الرابط إذا كان من YouTube
                if url.startswith(('http://', 'https://')):
                    # تحقق مما إذا كان الرابط من YouTube
                    is_youtube_url = 'youtube.com' in url or 'youtu.be' in url

                    if is_youtube_url and hasattr(self, '_extract_youtube_audio_url'):
                        # محاولة إعادة استخراج رابط الصوت من YouTube
                        try:
                            print("Trying to re-extract YouTube audio URL")
                            # استخدام معرف الفيديو المخزن إذا كان متاحًا
                            if hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                                video_id = self.online_song_video_id
                                print(f"Using stored video ID: {video_id}")
                            else:
                                # استخراج معرف الفيديو من الرابط
                                video_id = self._extract_youtube_video_id(url)
                            if video_id:
                                # إعادة بناء رابط YouTube
                                youtube_url = f"https://www.youtube.com/watch?v={video_id}"
                                # استخراج رابط الصوت
                                new_audio_url = self._extract_youtube_audio_url(youtube_url)

                                if new_audio_url:
                                    print(f"Successfully re-extracted audio URL: {new_audio_url}")
                                    # محاولة تشغيل الرابط الجديد
                                    return self.play_url_streaming(new_audio_url, title, artist, thumbnail_url)
                        except Exception as extract_error:
                            print(f"Error re-extracting YouTube audio URL: {extract_error}")

                    # إذا فشلت إعادة الاستخراج أو لم يكن الرابط من YouTube
                    self.show_error_dialog("تعذر تحميل الأغنية. قد تكون انتهت صلاحية الرابط. يرجى البحث عنها مرة أخرى.")
                return False

            print(f"Sound loaded successfully from URL: {url}")

            # إذا لم يتم تشغيل الصوت بالفعل في حالة التنزيل التدريجي
            if not self.is_playing:
                # ضبط مستوى الصوت
                self.sound.volume = self.volume

                # تشغيل الصوت
                self.sound.play()
                print("Sound play() called")

                # تعيين حالة التشغيل
                self.is_playing = True

            # تحديث واجهة المستخدم
            try:
                # تحديث اسم المسار في الشريط السفلي
                if hasattr(self.ids, 'current_track_name'):
                    self.ids.current_track_name.text = title
                    self.ids.current_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                    # تعيين محاذاة النص للنص العربي
                    is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in title)
                    if is_arabic:
                        self.ids.current_track_name.halign = 'right'
                        self.ids.current_track_name.text_language = 'ar'
                        self.ids.current_track_name.font_size = sp(16)
                    else:
                        self.ids.current_track_name.halign = 'left'
                        self.ids.current_track_name.font_size = sp(14)

                # تحديث شاشة التشغيل الآن إذا كانت مفتوحة
                if self.ids.screen_manager.current == 'now_playing':
                    # تحديث اسم المسار في شاشة التشغيل الآن
                    if hasattr(self.ids, 'now_playing_track_name'):
                        self.ids.now_playing_track_name.text = title
                        self.ids.now_playing_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                        # تعيين محاذاة النص للنص العربي
                        if is_arabic:
                            self.ids.now_playing_track_name.halign = 'right'
                            self.ids.now_playing_track_name.text_language = 'ar'
                            self.ids.now_playing_track_name.font_size = sp(18)
                        else:
                            self.ids.now_playing_track_name.halign = 'center'
                            self.ids.now_playing_track_name.font_size = sp(16)

                        # بدء تحريك النص إذا كان طويلاً
                        self.start_song_title_animation()

                    # تحديث اسم الفنان في شاشة التشغيل الآن
                    if hasattr(self.ids, 'now_playing_artist_name'):
                        self.ids.now_playing_artist_name.text = artist
                        self.ids.now_playing_artist_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                        # تعيين محاذاة النص للنص العربي
                        is_arabic_artist = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in artist)
                        if is_arabic_artist:
                            self.ids.now_playing_artist_name.halign = 'right'
                            self.ids.now_playing_artist_name.text_language = 'ar'
                            self.ids.now_playing_artist_name.font_size = sp(16)
                        else:
                            self.ids.now_playing_artist_name.halign = 'center'
                            self.ids.now_playing_artist_name.font_size = sp(14)
            except Exception as ui_error:
                print(f"Error updating UI: {ui_error}")

            # بدء مؤقت التقدم
            try:
                if hasattr(self, 'start_progress_timer'):
                    self.start_progress_timer()
            except Exception as timer_error:
                print(f"Error starting progress timer: {timer_error}")

            # تحديث زر التشغيل/الإيقاف المؤقت
            try:
                if hasattr(self, 'fix_play_button'):
                    self.fix_play_button()
            except Exception as button_error:
                print(f"Error fixing play button: {button_error}")

            # تحديث إشعار الوسائط على أندرويد
            try:
                if hasattr(self, 'update_media_notification'):
                    self.update_media_notification()
            except Exception as notif_error:
                logger.error(f"خطأ في تحديث إشعار الوسائط: {notif_error}")

            return True

        except Exception as e:
            print(f"Error playing URL: {e}")
            logger.error(f"Error playing URL: {e}")
            import traceback
            logger.error(traceback.format_exc())

            # محاولة تشغيل الصوت باستخدام طريقة play_track إذا كان الرابط محليًا
            if not url.startswith(('http://', 'https://')) and os.path.exists(url):
                print(f"Trying to play as local file using play_track after error")
                return self.play_track(url)

            return False

    def play_track(self, path):
        """تشغيل ملف صوتي بطريقة مباشرة جدًا"""
        print(f"Attempting to play: {path}")

        # Reset online song properties when playing a local song
        self.reset_online_song()

        if not path or not os.path.exists(path):
            print(f"File does not exist: {path}")
            return False

        try:
            # إيقاف الصوت الحالي وإزالته تمامًا
            if hasattr(self, 'sound') and self.sound:
                try:
                    print("Stopping current sound")
                    self.sound.stop()
                    # إلغاء أي مؤقتات مرتبطة بالصوت
                    self.stop_progress_timer()
                except Exception as stop_error:
                    print(f"Error stopping sound: {stop_error}")
                # تأكد من إزالة كائن الصوت القديم تمامًا
                self.sound = None
                # إضافة تأخير صغير للتأكد من إغلاق الصوت السابق
                time.sleep(0.1)

            # تحميل الملف الصوتي باستخدام SoundLoader
            print(f"Loading sound: {path}")
            from kivy.core.audio import SoundLoader
            # إعادة تعيين كائن الصوت
            self.sound = None

            # Aplicar mejoras de audio si está activado
            if hasattr(self, 'audio_enhancement_enabled') and self.audio_enhancement_enabled and hasattr(self, 'audio_enhancer'):
                print("Applying audio enhancements...")

                # Función para cargar el audio mejorado
                def load_enhanced_audio(enhanced_path):
                    print(f"Loading enhanced audio: {enhanced_path}")
                    self.sound = SoundLoader.load(enhanced_path)
                    if self.sound:
                        # Continuar con la reproducción
                        self._continue_playback(path)
                    else:
                        print(f"Failed to load enhanced audio, falling back to original")
                        # Cargar el audio original como respaldo
                        self.sound = SoundLoader.load(path)
                        self._continue_playback(path)

                # Mejorar el audio en segundo plano
                self.audio_enhancer.enhance_audio(path, load_enhanced_audio)
            else:
                # Cargar el audio original sin mejoras
                self.sound = SoundLoader.load(path)

            if not self.sound:
                print(f"Failed to load sound: {path}")
                return False

            print(f"Sound loaded successfully: {path}")

            # ضبط مستوى الصوت
            self.sound.volume = self.volume

            # إعادة تعيين المتغيرات المرتبطة بالتشغيل
            self.current_pos = 0
            self.play_start_time = time.time()
            self._paused_position = 0
            self._paused_sound_path = None

            # تشغيل الصوت
            print("Playing sound")
            self.sound.play()
            print("Sound play() called")

            # تعيين حالة التشغيل
            self.is_playing = True

            # تحديث واجهة المستخدم بطريقة أكثر كفاءة
            song_title = self.get_song_title(path)
            print(f"Updating UI with song title: {song_title}")

            # تحديث اسم المسار في الشريط السفلي فقط
            if hasattr(self.ids, 'current_track_name'):
                self.ids.current_track_name.text = song_title
                self.ids.current_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                # تعيين محاذاة النص للنص العربي
                is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in song_title)
                if is_arabic:
                    self.ids.current_track_name.halign = 'right'
                    self.ids.current_track_name.text_language = 'ar'
                    # ضبط حجم الخط للنص العربي
                    self.ids.current_track_name.font_size = sp(16)
                else:
                    # ضبط حجم الخط للنص الإنجليزي
                    self.ids.current_track_name.font_size = sp(14)

            # تحديث أزرار التشغيل
            self.fix_play_button()

            # تحديث وقت الأغنية في الشريط السفلي
            if hasattr(self.ids, 'current_time_main'):
                self.ids.current_time_main.text = self.format_time(0)
            if hasattr(self.ids, 'total_time_main') and hasattr(self.sound, 'length'):
                self.ids.total_time_main.text = self.format_time(self.sound.length)

            # بدء مؤقت التقدم
            self.start_progress_timer()

            # تحديث واجهة المستخدم
            self.update_ui_play_state()

            # تحديث البيانات الوصفية وصورة الغلاف في خلفية منفصلة
            def update_metadata_and_cover(dt):
                # تحديث البيانات الوصفية
                self.update_metadata(path)

                # تحديث صورة الغلاف
                self.update_album_cover(path)

                # تحديث اسم المسار في شاشة التشغيل الآن (فقط إذا كانت الشاشة مفتوحة)
                if self.ids.screen_manager.current == 'now_playing' and hasattr(self.ids, 'now_playing_track_name'):
                    self.ids.now_playing_track_name.text = song_title
                    self.ids.now_playing_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                    # تعيين محاذاة النص للنص العربي
                    if is_arabic:
                        self.ids.now_playing_track_name.halign = 'right'
                        self.ids.now_playing_track_name.text_language = 'ar'
                        self.ids.now_playing_track_name.font_size = sp(16)
                    else:
                        self.ids.now_playing_track_name.font_size = sp(16)

                    # بدء تحريك النص إذا كان طويلاً
                    self.start_song_title_animation()

            # جدولة تحديث البيانات الوصفية وصورة الغلاف بعد تأخير قصير
            from kivy.clock import Clock
            Clock.schedule_once(update_metadata_and_cover, 0.1)

            print("Track playing successfully")
            return True

        except Exception as e:
            print(f"Error playing track: {e}")
            logger.error(f"خطأ في تشغيل المسار: {e}")
            return False

    def update_metadata(self, path):
        try:
            if not path or not os.path.exists(path):
                return

            file_ext = os.path.splitext(path)[1].lower()
            duration = 0

            # Add a safety check for self.ids
            if not hasattr(self, 'ids'):
                logger.error("self.ids not available in update_metadata")
                return

            try:
                if file_ext == '.mp3':
                    audio = MP3(path)
                    duration = audio.info.length
                elif file_ext == '.flac':
                    audio = FLAC(path)
                    duration = audio.info.length
                elif file_ext == '.ogg':
                    audio = OggVorbis(path)
                    duration = audio.info.length
                elif file_ext == '.opus':
                    audio = OggOpus(path)
                    duration = audio.info.length
                elif file_ext in ['.m4a', '.mp4', '.aac']:
                    audio = MP4(path)
                    duration = audio.info.length
                elif file_ext in ['.wma', '.asf']:
                    audio = ASF(path)
                    duration = audio.info.length
                elif file_ext in ['.wav']:
                    try:
                        audio = WAVE(path)
                        duration = audio.info.length
                    except:
                        if self.sound:
                            duration = self.sound.length
                elif file_ext in ['.aiff', '.aif', '.aifc']:
                    try:
                        audio = AIFF(path)
                        duration = audio.info.length
                    except:
                        if self.sound:
                            duration = self.sound.length
                else:
                    if self.sound:
                        duration = self.sound.length
            except Exception as e:
                logger.debug(f"Error extracting metadata: {e}")
                if self.sound:
                    duration = self.sound.length

            # Update progress bar max value and total time
            if hasattr(self.ids, 'total_time_now_playing'):
                self.ids.total_time_now_playing.text = self.format_time(duration)

            # Set slider max to the duration in seconds
            max_val = duration if duration > 0 else 100
            if hasattr(self.ids, 'seek_slider_now_playing'):
                self.ids.seek_slider_now_playing.max = max_val

        except Exception as e:
            logger.error(f"Error in update_metadata: {e}")
            # Fallback for progress bar
            if self.sound and self.sound.length > 0:
                fallback = self.sound.length
                if hasattr(self.ids, 'total_time_now_playing'):
                    self.ids.total_time_now_playing.text = self.format_time(fallback)
                if hasattr(self.ids, 'seek_slider_now_playing'):
                    self.ids.seek_slider_now_playing.max = fallback

    def toggle_play(self):
        """تبديل حالة التشغيل/الإيقاف المؤقت"""
        try:
            # تبديل حالة التشغيل
            self.toggle_play_only()

            # تحديث أيقونة زر التشغيل/الإيقاف في شاشة التشغيل الآن
            if self.ids.screen_manager.current == 'now_playing':
                # البحث عن زر التشغيل/الإيقاف
                for widget in self.walk():
                    if hasattr(widget, 'id') and widget.id == 'play_pause_button':
                        # تحديث الأيقونة
                        widget.icon = 'pause' if self.is_playing else 'play'
                        break

        except Exception as e:
            logger.error(f"Error in toggle_play: {e}")

    def toggle_play_only(self):
        """تبديل حالة التشغيل/الإيقاف المؤقت للمسار الحالي"""
        try:
            print("Toggle play/pause called")

            # التحقق من وجود كائن الصوت
            if not hasattr(self, 'sound') or self.sound is None:
                print("No sound object, trying to play current or first track")
                # إذا لم يكن هناك صوت، قم بتشغيل المسار الحالي أو الأول في قائمة التشغيل
                if self.current_index >= 0:
                    print(f"Playing current track at index {self.current_index}")
                    self.play_track_by_index(self.current_index)
                elif self.playlist:
                    print("Playing first track in playlist")
                    self.play_track_by_index(0)
                return

            # تبديل حالة التشغيل
            if self.is_playing:
                print("Currently playing, pausing sound")

                # حفظ الموضع الحالي قبل الإيقاف
                try:
                    # الحصول على الموضع الحالي
                    if hasattr(self.sound, 'get_pos'):
                        try:
                            pos = self.sound.get_pos()
                            if pos >= 0:
                                self.current_pos = pos
                                print(f"Current position saved from get_pos: {self.current_pos}")
                            else:
                                # إذا كانت القيمة سالبة، نحسب الموضع من وقت البدء
                                elapsed = time.time() - self.play_start_time
                                self.current_pos = elapsed
                                print(f"Current position calculated from elapsed time (negative get_pos): {self.current_pos}")
                        except Exception as get_pos_error:
                            print(f"Error calling get_pos: {get_pos_error}")
                            # إذا فشل استدعاء get_pos، نحسب الموضع من وقت البدء
                            elapsed = time.time() - self.play_start_time
                            self.current_pos = elapsed
                            print(f"Current position calculated from elapsed time (get_pos error): {self.current_pos}")
                    else:
                        # إذا لم تكن دالة get_pos متوفرة، نحسب الموضع من وقت البدء
                        elapsed = time.time() - self.play_start_time
                        self.current_pos = elapsed
                        print(f"Current position calculated from elapsed time (no get_pos): {self.current_pos}")

                    # التأكد من أن الموضع المحفوظ ليس سالبًا أو صفرًا
                    if self.current_pos <= 0 and hasattr(self, 'play_start_time'):
                        elapsed = time.time() - self.play_start_time
                        if elapsed > 0:
                            self.current_pos = elapsed
                            print(f"Corrected position to elapsed time: {self.current_pos}")
                except Exception as pos_error:
                    print(f"Error getting position: {pos_error}")
                    # محاولة استخدام الوقت المنقضي كخطة بديلة
                    if hasattr(self, 'play_start_time'):
                        elapsed = time.time() - self.play_start_time
                        self.current_pos = elapsed
                        print(f"Fallback position from elapsed time: {self.current_pos}")
                    else:
                        self.current_pos = 0

                # حفظ مسار الصوت الحالي للاستئناف لاحقًا
                if self.is_online_song:
                    # إذا كانت الأغنية أونلاين، احفظ رابط الأغنية
                    self._paused_sound_path = self.online_song_url
                    print(f"Saved paused online URL: {self._paused_sound_path}")

                    # حفظ الموضع الحالي في متغير عام - التأكد من أن القيمة صالحة
                    if self.current_pos > 0:
                        self._paused_position = self.current_pos
                        print(f"Saved paused position: {self._paused_position}")
                    else:
                        # إذا كان الموضع الحالي غير صالح، حاول حساب الوقت المنقضي
                        if hasattr(self, 'play_start_time'):
                            elapsed = time.time() - self.play_start_time
                            if elapsed > 0:
                                self._paused_position = elapsed
                                print(f"Saved calculated paused position: {self._paused_position}")
                            else:
                                self._paused_position = 0
                                print("Could not determine valid position, saved 0")
                        else:
                            self._paused_position = 0
                            print("No play_start_time available, saved position 0")

                    # حفظ معلومات الأغنية الأونلاين
                    self._paused_online_title = self.online_song_title
                    self._paused_online_artist = self.online_song_artist
                    self._paused_online_thumbnail = self.online_song_thumbnail

                    # حفظ معرف الفيديو إذا كان متاحًا - هذا مهم جدًا لإعادة استخراج الرابط
                    if hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                        self._paused_online_video_id = self.online_song_video_id
                        print(f"Saved video ID for paused song: {self.online_song_video_id}")
                    else:
                        # محاولة استخراج معرف الفيديو من الرابط إذا لم يكن متاحًا
                        video_id = self._extract_youtube_video_id(self.online_song_url)
                        if video_id:
                            self._paused_online_video_id = video_id
                            print(f"Extracted and saved video ID for paused song: {video_id}")
                        else:
                            print("Warning: Could not extract video ID for paused online song")
                elif self.current_index >= 0:
                    playlist_to_use = self.favorites if self.is_favorites_visible else self.playlist
                    if self.current_index < len(playlist_to_use):
                        self._paused_sound_path = playlist_to_use[self.current_index]
                        print(f"Saved paused sound path: {self._paused_sound_path}")
                        # حفظ الموضع الحالي في متغير عام
                        self._paused_position = self.current_pos
                        print(f"Saved paused position: {self._paused_position}")

                # إيقاف الصوت مؤقتًا
                try:
                    # نستخدم خاصية الإيقاف المؤقت إذا كانت متوفرة
                    if hasattr(self.sound, 'pause'):
                        self.sound.pause()
                        print("Sound paused successfully")
                    else:
                        # إذا لم تكن خاصية الإيقاف المؤقت متوفرة، نستخدم stop
                        self.sound.stop()
                        print("Sound stopped (no pause method available)")
                except Exception as pause_error:
                    print(f"Error pausing sound: {pause_error}")

                # تحديث الحالة
                self.is_playing = False
                print("Set is_playing to False")

                # إيقاف مؤقت التقدم
                self.stop_progress_timer()

                # تحديث واجهة المستخدم
                self.fix_play_button()
            else:
                print("Currently paused, resuming playback")

                # استئناف التشغيل
                if hasattr(self, '_paused_sound_path') and self._paused_sound_path:
                    # التحقق مما إذا كان المسار المتوقف هو أغنية أونلاين
                    is_online_url = hasattr(self, '_paused_online_title') and self._paused_online_title
                    is_local_file = not is_online_url and os.path.exists(self._paused_sound_path)

                    if is_online_url or is_local_file:
                        print(f"Resuming from paused path: {self._paused_sound_path}")
                        print(f"Is online song: {is_online_url}")

                        # الحصول على الموضع المحفوظ
                        saved_position = getattr(self, '_paused_position', 0)
                        print(f"Retrieved saved position: {saved_position}")

                        # التحقق مما إذا كان الصوت الحالي هو نفس الملف المتوقف
                        same_sound_file = False
                        if hasattr(self, 'sound') and self.sound and hasattr(self.sound, 'source'):
                            # تنظيف المسارات للمقارنة
                            current_path = os.path.normpath(self.sound.source)
                            paused_path = os.path.normpath(self._paused_sound_path)
                            if current_path == paused_path:
                                same_sound_file = True
                                print("Same sound file detected, resuming without reloading")

                        if same_sound_file and hasattr(self.sound, 'play'):
                            # استئناف نفس الملف الصوتي بدون إعادة تحميله
                            print("Resuming existing sound object")
                            self.sound.play()

                            # الانتقال إلى الموضع المحفوظ
                            if saved_position > 0:
                                try:
                                    print(f"Seeking to position: {saved_position}")
                                    if hasattr(self.sound, 'seek'):
                                        try:
                                            self.sound.seek(saved_position)
                                            print("Direct seek successful")
                                        except Exception as direct_seek_error:
                                            print(f"Error in direct seek: {direct_seek_error}")
                                            # إذا فشل الانتقال المباشر، نحاول استخدام طريقة المحاكاة
                                            if hasattr(self, 'simulate_seek_with_restart'):
                                                success = self.simulate_seek_with_restart(saved_position)
                                                if success:
                                                    print("Used simulate_seek_with_restart as fallback")
                                    else:
                                        # إذا لم تكن طريقة seek متاحة، نستخدم طريقة المحاكاة
                                        if hasattr(self, 'simulate_seek_with_restart'):
                                            success = self.simulate_seek_with_restart(saved_position)
                                            if success:
                                                print("Used simulate_seek_with_restart (no seek method available)")
                                except Exception as seek_error:
                                    print(f"Error in all seeking attempts: {seek_error}")
                        else:
                            # إعادة تحميل الصوت
                            from kivy.core.audio import SoundLoader

                            # إذا كانت أغنية أونلاين، استعادة معلومات الأغنية
                            if is_online_url:
                                print("Restoring online song properties")
                                self.is_online_song = True
                                self.online_song_title = getattr(self, '_paused_online_title', "")
                                self.online_song_artist = getattr(self, '_paused_online_artist', "")
                                self.online_song_thumbnail = getattr(self, '_paused_online_thumbnail', "")
                                self.online_song_url = self._paused_sound_path
                                # استعادة معرف الفيديو إذا كان متاحًا
                                if hasattr(self, '_paused_online_video_id'):
                                    self.online_song_video_id = self._paused_online_video_id
                                    print(f"Restored video ID: {self._paused_online_video_id}")

                            # محاولة تحميل الصوت
                            try:
                                new_sound = SoundLoader.load(self._paused_sound_path)

                                if not new_sound:
                                    print(f"Failed to load sound: {self._paused_sound_path}")

                                    # إذا كان الرابط من YouTube، حاول إعادة استخراج الرابط
                                    if is_online_url and ('youtube.com' in self._paused_sound_path or 'youtu.be' in self._paused_sound_path or 'googlevideo.com' in self._paused_sound_path):
                                        print("Trying to re-extract YouTube audio URL")
                                        # استخراج معرف الفيديو من الرابط أو استخدام المعرف المخزن
                                        video_id = None

                                        # استخدام معرف الفيديو المخزن إذا كان متاحًا - هذا هو الخيار الأفضل
                                        if hasattr(self, '_paused_online_video_id') and self._paused_online_video_id:
                                            video_id = self._paused_online_video_id
                                            print(f"Using stored video ID: {video_id}")
                                        else:
                                            # محاولة استخراج معرف الفيديو من عنوان الأغنية إذا كان يحتوي على معرف YouTube
                                            if hasattr(self, '_paused_online_title') and self._paused_online_title:
                                                import re
                                                # البحث عن معرف YouTube في العنوان (غالبًا ما يكون في نهاية العنوان)
                                                match = re.search(r'\[([a-zA-Z0-9_-]{11})\]$', self._paused_online_title)
                                                if match:
                                                    video_id = match.group(1)
                                                    print(f"Found video ID in title: {video_id}")

                                            # إذا لم يتم العثور على معرف في العنوان، حاول استخراجه من الرابط
                                            if not video_id:
                                                video_id = self._extract_youtube_video_id(self._paused_sound_path)

                                        if video_id:
                                            print(f"Extracting audio URL from YouTube: https://www.youtube.com/watch?v={video_id}")
                                            # إعادة بناء رابط YouTube
                                            youtube_url = f"https://www.youtube.com/watch?v={video_id}"
                                            # استخراج رابط الصوت
                                            try:
                                                # استخدام طريقة refresh_youtube_url بدلاً من _extract_youtube_audio_url مباشرة
                                                new_audio_url = self.refresh_youtube_url(video_id)

                                                if new_audio_url:
                                                    print(f"Successfully re-extracted audio URL: {new_audio_url}")
                                                    # تحديث الرابط المخزن
                                                    self.online_song_url = new_audio_url
                                                    # استعادة معرف الفيديو
                                                    self.online_song_video_id = video_id
                                                    # طباعة الموضع المحفوظ للتأكد من صحته
                                                    print(f"DEBUG: Resuming from position: {self._paused_position}")

                                                    # تحقق من صحة الموضع المحفوظ
                                                    saved_position = self._paused_position
                                                    if saved_position <= 0 and hasattr(self, 'play_start_time'):
                                                        # حساب الموضع من وقت البدء والوقت المنقضي
                                                        elapsed = time.time() - self.play_start_time
                                                        if elapsed > 0:
                                                            saved_position = elapsed
                                                            print(f"Corrected position to: {saved_position}")

                                                    # محاولة تشغيل الرابط الجديد مع استئناف من الموضع المحفوظ
                                                    return self.play_url_streaming(new_audio_url, self._paused_online_title, self._paused_online_artist, self._paused_online_thumbnail, resume_position=saved_position)
                                                else:
                                                    print("Failed to extract audio URL, no URL returned")
                                                    # محاولة استخدام طريقة التنزيل المباشر كخطة بديلة
                                                    print("Trying direct download method as fallback")
                                                    if hasattr(self, 'download_and_play_youtube'):
                                                        success = self.download_and_play_youtube(
                                                            video_id,
                                                            self._paused_online_title,
                                                            self._paused_online_artist,
                                                            self._paused_online_thumbnail,
                                                            resume_position=self._paused_position
                                                        )
                                                        if success:
                                                            print("Successfully used download_and_play_youtube")
                                                            return True

                                                    # إذا فشلت طريقة التنزيل المباشر، نستخدم طريقة التنزيل العادية
                                                    print("Trying regular download method")
                                                    return self.play_url(youtube_url, self._paused_online_title, self._paused_online_artist, self._paused_online_thumbnail)
                                            except Exception as extract_error:
                                                print(f"Error re-extracting YouTube audio URL: {extract_error}")
                                                import traceback
                                                print(traceback.format_exc())

                                                # محاولة استخدام طريقة التنزيل المباشر كخطة بديلة
                                                try:
                                                    print("Trying direct download method after extraction error")
                                                    if hasattr(self, 'download_and_play_youtube'):
                                                        success = self.download_and_play_youtube(
                                                            video_id,
                                                            self._paused_online_title,
                                                            self._paused_online_artist,
                                                            self._paused_online_thumbnail,
                                                            resume_position=self._paused_position
                                                        )
                                                        if success:
                                                            print("Successfully used download_and_play_youtube after error")
                                                            return True

                                                    # إذا فشلت طريقة التنزيل المباشر، نستخدم طريقة التنزيل العادية
                                                    print("Trying regular download method after extraction error")
                                                    return self.play_url(youtube_url, self._paused_online_title, self._paused_online_artist, self._paused_online_thumbnail)
                                                except Exception as download_error:
                                                    print(f"Error using download methods: {download_error}")

                                    # إظهار رسالة خطأ للمستخدم
                                    if is_online_url:
                                        self.show_error_dialog("انتهت صلاحية رابط الأغنية. يرجى البحث عنها مرة أخرى.")
                                    return
                            except Exception as load_error:
                                print(f"Error loading sound: {load_error}")
                                # إظهار رسالة خطأ للمستخدم
                                if is_online_url:
                                    self.show_error_dialog("حدث خطأ أثناء تحميل الأغنية. يرجى البحث عنها مرة أخرى.")
                                return

                            print(f"Sound loaded successfully: {self._paused_sound_path}")

                            # تعيين الصوت الجديد
                            self.sound = new_sound

                            # ضبط مستوى الصوت
                            self.sound.volume = self.volume

                            # تشغيل الصوت
                            print("Playing sound")
                            self.sound.play()
                            print("Sound play() called")

                            # الانتقال إلى الموضع المحفوظ
                            if saved_position > 0:
                                try:
                                    print(f"Seeking to position: {saved_position}")
                                    # تأخير قصير قبل الانتقال للتأكد من أن الصوت جاهز
                                    def delayed_seek(dt):
                                        try:
                                            if hasattr(self.sound, 'seek'):
                                                try:
                                                    self.sound.seek(saved_position)
                                                    print(f"Delayed seek successful")
                                                except Exception as direct_seek_error:
                                                    print(f"Error in delayed direct seek: {direct_seek_error}")
                                                    # إذا فشل الانتقال المباشر، نحاول استخدام طريقة المحاكاة
                                                    if hasattr(self, 'simulate_seek_with_restart'):
                                                        success = self.simulate_seek_with_restart(saved_position)
                                                        if success:
                                                            print("Used simulate_seek_with_restart in delayed seek")
                                            else:
                                                # إذا لم تكن طريقة seek متاحة، نستخدم طريقة المحاكاة
                                                if hasattr(self, 'simulate_seek_with_restart'):
                                                    success = self.simulate_seek_with_restart(saved_position)
                                                    if success:
                                                        print("Used simulate_seek_with_restart in delayed seek (no seek method)")
                                        except Exception as delayed_seek_error:
                                            print(f"Error in delayed seek: {delayed_seek_error}")

                                    # جدولة الانتقال بعد 0.5 ثانية (زيادة التأخير للتأكد من جاهزية الصوت)
                                    from kivy.clock import Clock
                                    Clock.schedule_once(delayed_seek, 0.5)
                                except Exception as seek_error:
                                    print(f"Error scheduling seek: {seek_error}")

                        # تعيين حالة التشغيل
                        self.is_playing = True
                        self.current_pos = saved_position
                        self.play_start_time = time.time() - saved_position

                        # بدء مؤقت التقدم
                        self.start_progress_timer()

                        # تحديث واجهة المستخدم
                        self.fix_play_button()

                        # تحديث واجهة المستخدم للأغنية الأونلاين إذا لزم الأمر
                        if is_online_url:
                            # تحديث اسم المسار في الشريط السفلي
                            if hasattr(self.ids, 'current_track_name'):
                                self.ids.current_track_name.text = self.online_song_title

                            # تحديث شاشة التشغيل الآن إذا كانت مفتوحة
                            if self.ids.screen_manager.current == 'now_playing':
                                self.update_now_playing_ui()

                        print("Playback resumed successfully")
                elif hasattr(self, 'is_online_song') and self.is_online_song and hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                    print("Trying to resume online song using video ID")
                    # محاولة استئناف الأغنية الأونلاين باستخدام معرف الفيديو
                    video_id = self.online_song_video_id
                    youtube_url = f"https://www.youtube.com/watch?v={video_id}"

                    # حفظ الموضع الحالي للاستئناف
                    saved_position = 0
                    if hasattr(self, 'current_pos') and self.current_pos > 0:
                        saved_position = self.current_pos
                        print(f"Saved current position for resuming: {saved_position}")

                    # استخراج رابط الصوت الجديد
                    try:
                        new_audio_url = self._extract_youtube_audio_url(youtube_url)
                        if new_audio_url:
                            print(f"Successfully extracted new audio URL for online song")
                            # تشغيل الأغنية الأونلاين مع الاحتفاظ بالمعلومات الحالية والموضع
                            return self.play_url_streaming(new_audio_url, self.online_song_title, self.online_song_artist, self.online_song_thumbnail, resume_position=saved_position)
                    except Exception as e:
                        print(f"Error resuming online song: {e}")

                # إذا فشلت كل المحاولات السابقة، نحاول تشغيل المسار الحالي أو الأول
                elif self.current_index >= 0:
                    print(f"Restarting current track at index {self.current_index}")
                    # إعادة تشغيل المسار الحالي
                    self.play_track_by_index(self.current_index)
                elif self.playlist:
                    print("Playing first track in playlist")
                    # تشغيل المسار الأول
                    self.play_track_by_index(0)
                else:
                    print("No track to play")

            # تحديث إشعار الوسائط على أندرويد
            if platform == 'android':
                try:
                    self.update_media_notification()
                except Exception as notif_error:
                    logger.error(f"خطأ في تحديث إشعار الوسائط: {notif_error}")

            # تحديث واجهة المستخدم
            self.update_ui_play_state()

        except Exception as e:
            logger.error(f"خطأ في toggle_play_only: {e}")

    def update_ui_play_state(self):
        """تحديث حالة أزرار التشغيل/الإيقاف في واجهة المستخدم"""
        try:
            # تحديث أيقونة زر التشغيل في الشريط السفلي
            if hasattr(self.ids, 'bottom_bar') and hasattr(self.ids.bottom_bar, 'children'):
                for child in self.ids.bottom_bar.walk():
                    if isinstance(child, MDIconButton) and hasattr(child, 'icon'):
                        if 'play' in child.icon or 'pause' in child.icon:
                            child.icon = 'pause' if self.is_playing else 'play'

            # تحديث واجهة المستخدم في شاشة التشغيل الآن
            if hasattr(self.ids, 'screen_manager') and self.ids.screen_manager.current == 'now_playing':
                # الحصول على الموضع الحالي
                current_pos = 0

                # استخدام الموضع المحفوظ سابقًا إذا كان متاحًا
                if hasattr(self, 'current_pos'):
                    current_pos = self.current_pos

                # التحقق من وجود كائن الصوت
                if hasattr(self, 'sound') and self.sound is not None:
                    try:
                        # محاولة استخدام get_pos
                        if hasattr(self.sound, 'get_pos'):
                            try:
                                current_pos = self.sound.get_pos()
                                if current_pos < 0:
                                    # استخدام الموضع المحفوظ سابقًا
                                    if hasattr(self, 'current_pos') and self.current_pos >= 0:
                                        current_pos = self.current_pos
                                    else:
                                        # استخدام الوقت المنقضي منذ بدء التشغيل
                                        if hasattr(self, 'play_start_time'):
                                            elapsed_time = time.time() - self.play_start_time
                                            current_pos = elapsed_time
                            except Exception as pos_error:
                                logger.warning(f"خطأ في استدعاء get_pos: {pos_error}")
                    except Exception as pos_error:
                        logger.warning(f"خطأ في الحصول على الموضع الحالي: {pos_error}")

                # تحديث شريط التقدم
                if hasattr(self.ids, 'seek_slider_now_playing'):
                    try:
                        self.ids.seek_slider_now_playing.value = current_pos
                    except Exception as slider_error:
                        logger.warning(f"خطأ في تحديث شريط التقدم: {slider_error}")

                # تحديث عرض الوقت
                if hasattr(self.ids, 'current_time_now_playing'):
                    try:
                        self.ids.current_time_now_playing.text = self.format_time(current_pos)
                    except Exception as time_error:
                        logger.warning(f"خطأ في تحديث عرض الوقت: {time_error}")
                        self.ids.current_time_now_playing.text = "00:00"

        except Exception as e:
            logger.error(f"خطأ في update_ui_play_state: {e}")

    def start_progress_timer(self):
        """بدء مؤقت لتحديث شريط التقدم"""
        # إلغاء أي مؤقت موجود
        self.stop_progress_timer()

        # تحديد الفاصل الزمني المناسب بناءً على نوع الأغنية وحالة التطبيق
        is_online = getattr(self, 'is_online_song', False)
        is_foreground = True  # افتراضيًا، نفترض أن التطبيق في المقدمة

        # على أندرويد، يمكننا التحقق مما إذا كان التطبيق في المقدمة
        try:
            from jnius import autoclass
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            if PythonActivity.mActivity:
                is_foreground = PythonActivity.mActivity.hasWindowFocus()
        except:
            pass

        # تحديد الفاصل الزمني المناسب:
        # - للأغاني المحلية في المقدمة: 0.1 ثانية (تحديث سريع)
        # - للأغاني الأونلاين في المقدمة: 0.3 ثانية (تحديث أبطأ لتحسين الأداء)
        # - للتطبيق في الخلفية: 0.5 ثانية (تحديث بطيء جدًا لتوفير البطارية)
        if not is_foreground:
            interval = 0.5  # أبطأ تحديث عندما يكون التطبيق في الخلفية
        elif is_online:
            interval = 0.3  # أبطأ للأغاني الأونلاين
        else:
            interval = 0.1  # أسرع للأغاني المحلية

        # تخزين الفاصل الزمني للاستخدام في المستقبل
        self._progress_timer_interval = interval

        # إنشاء مؤقت جديد
        self.progress_timer = Clock.schedule_interval(self.update_progress, interval)

        # تسجيل وقت بدء التشغيل إذا لم يكن موجودًا
        if not hasattr(self, 'play_start_time') or self.play_start_time <= 0:
            self.play_start_time = time.time()

        # إعادة ضبط عداد التحديث
        if hasattr(self, '_update_counter'):
            self._update_counter = 0

        print(f"Started progress timer with interval: {interval}s")

    def stop_progress_timer(self):
        """إيقاف مؤقت تحديث شريط التقدم"""
        # التحقق من وجود المؤقت قبل إلغائه
        if hasattr(self, 'progress_timer') and self.progress_timer:
            self.progress_timer.cancel()
            self.progress_timer = None
            print("Stopped progress timer")

    def _continue_playback(self, original_path):
        """Continúa la reproducción después de mejorar el audio"""
        try:
            if not self.sound:
                print(f"No sound object available")
                return False

            print(f"Continuing playback with enhanced audio")

            # ضبط مستوى الصوت
            self.sound.volume = self.volume

            # إعادة تعيين المتغيرات المرتبطة بالتشغيل
            self.current_pos = 0
            self.play_start_time = time.time()
            self._paused_position = 0
            self._paused_sound_path = None

            # تشغيل الصوت
            print("Playing enhanced sound")
            self.sound.play()
            print("Enhanced sound play() called")

            # تعيين حالة التشغيل
            self.is_playing = True

            # تحديث واجهة المستخدم بطريقة أكثر كفاءة
            song_title = self.get_song_title(original_path)
            print(f"Updating UI with song title: {song_title}")

            # تحديث اسم المسار في الشريط السفلي فقط
            if hasattr(self.ids, 'current_track_name'):
                self.ids.current_track_name.text = song_title
                self.ids.current_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                # تعيين محاذاة النص للنص العربي
                is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in song_title)
                if is_arabic:
                    self.ids.current_track_name.halign = 'right'
                    self.ids.current_track_name.text_language = 'ar'
                    # ضبط حجم الخط للنص العربي
                    self.ids.current_track_name.font_size = sp(16)
                else:
                    # ضبط حجم الخط للنص الإنجليزي
                    self.ids.current_track_name.font_size = sp(14)

            # تحديث أزرار التشغيل
            self.fix_play_button()

            # تحديث وقت الأغنية في الشريط السفلي
            if hasattr(self.ids, 'current_time_main'):
                self.ids.current_time_main.text = self.format_time(0)
            if hasattr(self.ids, 'total_time_main') and hasattr(self.sound, 'length'):
                self.ids.total_time_main.text = self.format_time(self.sound.length)

            # بدء مؤقت التقدم
            self.start_progress_timer()

            # تحديث واجهة المستخدم
            self.update_ui_play_state()

            # تحديث البيانات الوصفية وصورة الغلاف في خلفية منفصلة
            def update_metadata_and_cover(dt):
                # تحديث البيانات الوصفية
                self.update_metadata(original_path)

                # تحديث صورة الغلاف
                self.update_album_cover(original_path)

                # تحديث شاشة التشغيل الآن إذا كانت مفتوحة
                if self.ids.screen_manager.current == 'now_playing':
                    self.update_now_playing_ui()
                else:
                    # تحديث اسم المسار في الشريط السفلي فقط
                    if hasattr(self.ids, 'current_track_name'):
                        self.ids.current_track_name.text = song_title
                        self.ids.current_track_name.font_name = 'NotoNaskhArabic-VariableFont_wght'

                        # تعيين محاذاة النص للنص العربي
                        if is_arabic:
                            self.ids.current_track_name.halign = 'right'
                            self.ids.current_track_name.text_language = 'ar'
                            self.ids.current_track_name.font_size = sp(16)
                        else:
                            self.ids.current_track_name.font_size = sp(14)

            # جدولة تحديث البيانات الوصفية وصورة الغلاف بعد تأخير قصير
            from kivy.clock import Clock
            Clock.schedule_once(update_metadata_and_cover, 0.1)

            print("Enhanced track playing successfully")
            return True

        except Exception as e:
            print(f"Error in _continue_playback: {e}")
            logger.error(f"خطأ في _continue_playback: {e}")
            return False

    def replace_slider(self, dt):
        """استبدال شريط التقدم بشريط تقدم مخصص"""
        try:
            # البحث عن شاشة التشغيل الآن
            now_playing = None
            for widget in self.walk(restrict=True):
                if hasattr(widget, 'name') and widget.name == 'now_playing':
                    now_playing = widget
                    break

            if now_playing:
                # البحث عن حاوية شريط التقدم
                for widget in now_playing.walk(restrict=True):
                    if isinstance(widget, BoxLayout) and hasattr(widget, 'orientation') and widget.orientation == 'vertical':
                        # التحقق مما إذا كانت هذه هي حاوية شريط التقدم
                        for child in list(widget.children):
                            if hasattr(child, 'id') and getattr(child, 'id', '') == 'seek_slider_now_playing':
                                # استبدال شريط التقدم بشريط التقدم المخصص
                                widget.remove_widget(child)

                                # إنشاء شريط تقدم مخصص بشكل صحيح
                                from custom_slider import CustomSlider
                                custom_slider = CustomSlider()
                                custom_slider.id = 'seek_slider_now_playing'

                                # إضافة الشريط إلى الحاوية
                                widget.add_widget(custom_slider)

                                # تعيين الخصائص الأساسية
                                if hasattr(self, 'sound') and self.sound and hasattr(self.sound, 'length'):
                                    custom_slider.max = self.sound.length
                                else:
                                    custom_slider.max = 100

                                custom_slider.value = 0
                                custom_slider.time_text = "00:00"

                                print("تم استبدال شريط التقدم بنجاح!")
                                break
        except Exception as e:
            print(f"خطأ في استبدال شريط التقدم: {e}")

    def on_slider_down(self):
        """تُستدعى عندما يبدأ المستخدم في سحب شريط التقدم"""
        self.user_seeking = True
        print("بدأ المستخدم البحث")

    def on_slider_up(self, value):
        """تُستدعى عندما يرفع المستخدم إصبعه عن شريط التقدم بعد السحب"""
        try:
            if self.sound:
                # التأكد من أن القيمة ضمن النطاق الصحيح
                value = max(0, min(value, self.sound.length))

                # الانتقال إلى الموضع الجديد
                self.sound.seek(value)
                print(f"تم الانتقال إلى الموضع: {value}")

                # تحديث عرض الوقت
                if self.ids.screen_manager.current == 'now_playing':
                    self.ids.current_time_now_playing.text = self.format_time(value)

                # تخزين الموضع الحالي
                self.current_pos = value
        except Exception as e:
            print(f"خطأ في on_slider_up: {e}")
        finally:
            # إعادة تعيين علامة البحث دائمًا
            self.user_seeking = False

    def update_progress_old(self, dt):
        """دالة تحديث شريط التقدم البسيطة والمباشرة (قديمة)"""
        try:
            # تحقق من وجود الصوت
            if not hasattr(self, 'sound') or self.sound is None:
                return

            # تحقق من حالة التشغيل
            try:
                if not hasattr(self.sound, 'state'):
                    return
                if self.sound.state != 'play' or self.user_seeking:
                    return
            except Exception as state_error:
                logger.warning(f"خطأ في التحقق من حالة الصوت: {state_error}")
                return

            # الحصول على الموضع الحالي بالثواني
            current_pos = 0
            try:
                # محاولة استخدام get_pos
                if hasattr(self.sound, 'get_pos'):
                    try:
                        current_pos = self.sound.get_pos()
                    except Exception as pos_error:
                        logger.warning(f"خطأ في استدعاء get_pos: {pos_error}")
                        # استخدام الموضع المحفوظ سابقًا
                        if hasattr(self, 'current_pos'):
                            current_pos = self.current_pos
                else:
                    logger.warning("كائن الصوت لا يحتوي على طريقة get_pos")
                    # استخدام الموضع المحفوظ سابقًا
                    if hasattr(self, 'current_pos'):
                        current_pos = self.current_pos
            except Exception as pos_error:
                logger.warning(f"خطأ في الحصول على الموضع الحالي: {pos_error}")
                # استخدام الموضع المحفوظ سابقًا
                if hasattr(self, 'current_pos'):
                    current_pos = self.current_pos

            # إذا كان الموضع غير صالح، استخدم الوقت المنقضي
            if current_pos < 0:
                # استخدام الموضع المحفوظ سابقًا إذا كان متاحًا
                if hasattr(self, 'current_pos') and self.current_pos >= 0:
                    current_pos = self.current_pos
                else:
                    # حساب الوقت المنقضي منذ بدء التشغيل
                    if hasattr(self, 'play_start_time'):
                        current_time = time.time()
                        elapsed_time = current_time - self.play_start_time
                        current_pos = elapsed_time
                    else:
                        current_pos = 0

            # تخزين الموضع الحالي للاستئناف لاحقًا
            self.current_pos = current_pos

            # تحديث شريط التقدم فقط إذا كنا في شاشة التشغيل الآن
            try:
                if hasattr(self.ids, 'screen_manager') and self.ids.screen_manager.current == 'now_playing':
                    # البحث عن شريط التقدم المخصص
                    for widget in self.walk(restrict=True):
                        if isinstance(widget, CustomSlider) and hasattr(widget, 'id') and widget.id == 'seek_slider_now_playing':
                            slider = widget

                            # تعيين القيمة القصوى إذا لزم الأمر
                            try:
                                if hasattr(self.sound, 'length') and self.sound.length > 0 and slider.max != self.sound.length:
                                    slider.max = self.sound.length
                            except Exception as length_error:
                                logger.warning(f"خطأ في الحصول على طول المسار: {length_error}")

                            # تحديث قيمة شريط التقدم مباشرة - هذا هو الجزء الأساسي
                            try:
                                if not slider.is_seeking:
                                    slider.value = current_pos
                            except Exception as slider_error:
                                logger.warning(f"خطأ في تحديث قيمة شريط التقدم: {slider_error}")

                            # تحديث تسميات الوقت
                            try:
                                if hasattr(self.ids, 'current_time_now_playing'):
                                    self.ids.current_time_now_playing.text = self.format_time(current_pos)
                                if hasattr(self.ids, 'total_time_now_playing') and hasattr(self.sound, 'length'):
                                    self.ids.total_time_now_playing.text = self.format_time(self.sound.length)
                            except Exception as time_error:
                                logger.warning(f"خطأ في تحديث تسميات الوقت: {time_error}")

                            break
            except Exception as ui_error:
                logger.warning(f"خطأ في تحديث واجهة المستخدم: {ui_error}")

            # التحقق مما إذا كان المسار قد انتهى
            try:
                if hasattr(self.sound, 'length') and current_pos >= self.sound.length - 0.5:
                    self.on_track_finish()
            except Exception as finish_error:
                logger.warning(f"خطأ في التحقق من انتهاء المسار: {finish_error}")

        except Exception as e:
            # تسجيل الخطأ والاستمرار
            logger.error(f"خطأ عام في update_progress: {e}")

    def seek_to(self, position):
        """الانتقال إلى موضع محدد في المسار الحالي"""
        try:
            # التحقق من وجود كائن الصوت
            if not hasattr(self, 'sound') or self.sound is None:
                logger.warning("لا يوجد مسار للانتقال إليه")
                return False

            # تعيين علامة البحث
            self.user_seeking = True

            # التحقق من وجود خاصية length
            if not hasattr(self.sound, 'length'):
                logger.warning("كائن الصوت لا يحتوي على خاصية length")
                self.user_seeking = False
                return False

            # التأكد من أن القيمة ضمن النطاق الصحيح
            try:
                max_position = self.sound.length
                if max_position <= 0:
                    max_position = 100  # قيمة افتراضية إذا كان الطول غير صالح
                position = max(0, min(position, max_position))
            except Exception as length_error:
                logger.error(f"خطأ في الحصول على طول المسار: {length_error}")
                position = max(0, min(position, 100))  # قيمة افتراضية

            # التحقق من وجود دالة seek
            if not hasattr(self.sound, 'seek'):
                logger.warning("كائن الصوت لا يحتوي على دالة seek")
                self.user_seeking = False
                return False

            # الانتقال إلى الموضع الجديد
            try:
                self.sound.seek(position)
                logger.info(f"تم الانتقال إلى الموضع: {position}")
            except Exception as seek_error:
                logger.error(f"خطأ في الانتقال إلى الموضع: {seek_error}")
                self.user_seeking = False
                return False

            # تحديث عرض الوقت
            if self.ids.screen_manager.current == 'now_playing':
                if hasattr(self.ids, 'current_time_now_playing'):
                    self.ids.current_time_now_playing.text = self.format_time(position)

            # تحديث شريط التقدم مباشرة
            if hasattr(self.ids, 'seek_slider_now_playing'):
                self.ids.seek_slider_now_playing.value = position

            # تحديث شريط التقدم الدائري
            if hasattr(self.ids, 'play_progress_main'):
                self.ids.play_progress_main.value = position
                self.ids.play_progress_main.draw()

            # تحديث الموضع الحالي
            self.current_pos = position

            # تحديث وقت البدء لمراعاة الموضع الجديد
            if hasattr(self, 'start_time') and hasattr(self, 'total_paused_time'):
                self.start_time = time.time() - position - self.total_paused_time

            # إعادة تعيين علامة البحث بعد تأخير قصير
            Clock.schedule_once(lambda dt: setattr(self, 'user_seeking', False), 0.1)
            return True
        except Exception as e:
            logger.error(f"خطأ في seek_to: {e}")
            self.user_seeking = False
            return False

    def next_track(self):
        """الانتقال إلى المسار التالي وتشغيله من البداية"""
        print("Next track function called")
        try:
            # الحصول على قائمة التشغيل المناسبة
            playlist = self.favorites if self.is_favorites_visible else self.playlist
            if not playlist:
                print("Playlist is empty")
                return False

            # تحديد المؤشر التالي
            old_index = self.current_index
            if self.shuffle:
                import random
                if len(playlist) > 1:
                    # تأكد من أن المؤشر الجديد مختلف عن المؤشر القديم
                    new_index = self.current_index
                    while new_index == self.current_index and len(playlist) > 1:
                        new_index = random.randint(0, len(playlist) - 1)
                    self.current_index = new_index
                else:
                    self.current_index = 0
            else:
                self.current_index = (self.current_index + 1) % len(playlist)

            print(f"Moving from index {old_index} to {self.current_index}")

            # استخدام play_track_by_index لتشغيل المسار الجديد
            # لا حاجة لإيقاف الصوت الحالي هنا لأن play_track_by_index ستقوم بذلك
            result = self.play_track_by_index(self.current_index)

            print(f"Next track play result: {result}")
            return result

        except Exception as e:
            print(f"Error in next_track: {e}")
            logger.error(f"Error in next_track: {e}")
            return False

    def prev_track(self):
        """الانتقال إلى المسار السابق وتشغيله من البداية"""
        print("Previous track function called")
        try:
            # الحصول على قائمة التشغيل المناسبة
            playlist = self.favorites if self.is_favorites_visible else self.playlist
            if not playlist:
                print("Playlist is empty")
                return False

            # تحديد المؤشر السابق
            old_index = self.current_index
            if self.shuffle:
                import random
                if len(playlist) > 1:
                    # تأكد من أن المؤشر الجديد مختلف عن المؤشر القديم
                    new_index = self.current_index
                    while new_index == self.current_index and len(playlist) > 1:
                        new_index = random.randint(0, len(playlist) - 1)
                    self.current_index = new_index
                else:
                    self.current_index = 0
            else:
                self.current_index = (self.current_index - 1) % len(playlist)

            print(f"Moving from index {old_index} to {self.current_index}")

            # استخدام play_track_by_index لتشغيل المسار الجديد
            # لا حاجة لإيقاف الصوت الحالي هنا لأن play_track_by_index ستقوم بذلك
            result = self.play_track_by_index(self.current_index)

            print(f"Previous track play result: {result}")
            return result

        except Exception as e:
            print(f"Error in prev_track: {e}")
            logger.error(f"Error in prev_track: {e}")
            return False

    def toggle_shuffle(self):
        self.shuffle = not self.shuffle

    def toggle_repeat(self):
        self.repeat = not self.repeat

    def update_progress(self, dt):
        """تحديث شريط التقدم وعرض الوقت بناءً على موضع التشغيل الحالي"""
        # التحقق من وجود الصوت قبل محاولة تحديث شريط التقدم
        if not hasattr(self, 'sound') or self.sound is None:
            return

        # تحسين الأداء: تحديث أقل تكرارًا للأغاني الأونلاين
        is_online = getattr(self, 'is_online_song', False)

        # التحقق مما إذا كان التطبيق في الخلفية
        is_foreground = True
        if platform == 'android':
            try:
                from jnius import autoclass
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                activity = PythonActivity.mActivity
                is_foreground = activity.hasWindowFocus()
            except:
                pass

        # تحديد معدل التحديث بناءً على حالة التطبيق ونوع الأغنية
        # - للتطبيق في الخلفية: تحديث كل 5 مرات (لتوفير البطارية)
        # - للأغاني الأونلاين: تحديث كل 3 مرات (لتحسين الأداء)
        # - للأغاني المحلية: تحديث كل مرة

        # تهيئة عداد التحديث إذا لم يكن موجودًا
        if not hasattr(self, '_update_counter'):
            self._update_counter = 0

        # زيادة العداد
        self._update_counter += 1

        # تحديد ما إذا كان يجب تحديث واجهة المستخدم في هذه الدورة
        update_ui = False

        if not is_foreground:
            # التطبيق في الخلفية، تحديث كل 5 مرات
            if self._update_counter >= 5:
                update_ui = True
                self._update_counter = 0
        elif is_online:
            # أغنية أونلاين، تحديث كل 3 مرات
            if self._update_counter >= 3:
                update_ui = True
                self._update_counter = 0
        else:
            # أغنية محلية، تحديث في كل مرة
            update_ui = True
            self._update_counter = 0

        # تحقق من مستوى تحسين الأداء إذا كان متاحًا
        if hasattr(self, 'performance_optimizer') and hasattr(self.performance_optimizer, 'optimization_level'):
            # إذا كان مستوى التحسين عالي (3)، قلل من معدل التحديث أكثر
            if self.performance_optimizer.optimization_level == 3 and update_ui and self._update_counter % 2 == 0:
                update_ui = False

        # التحقق من حالة التشغيل والبحث
        try:
            if hasattr(self.sound, 'state') and (self.sound.state != 'play' or self.user_seeking):
                return
        except Exception as state_error:
            # تحقق مما إذا كان هذا بسبب انتهاء صلاحية رابط YouTube
            if is_online and hasattr(self, 'online_song_video_id') and self.online_song_video_id:
                # تجنب محاولات التحديث المتكررة
                if hasattr(self, '_last_refresh_attempt') and time.time() - self._last_refresh_attempt < 10:
                    return  # تجنب محاولة التحديث أكثر من مرة كل 10 ثوانٍ

                self._last_refresh_attempt = time.time()
                print("Detected possible YouTube URL expiration, attempting to refresh")

                # استخدام طريقة التنزيل المباشر بدلاً من محاولة تحديث الرابط
                try:
                    if hasattr(self, 'download_and_play_youtube') and hasattr(self, 'online_song_video_id'):
                        video_id = self.online_song_video_id
                        title = getattr(self, 'online_song_title', "")
                        artist = getattr(self, 'online_song_artist', "")
                        thumbnail = getattr(self, 'online_song_thumbnail', "")
                        position = getattr(self, 'current_pos', 0)

                        print(f"Using download_and_play_youtube as fallback for expired URL, position: {position}")
                        self.download_and_play_youtube(video_id, title, artist, thumbnail, resume_position=position)
                        return
                except Exception as download_error:
                    print(f"Error using download fallback: {download_error}")
            return

        # الحصول على الموضع الحالي بالثواني - استخدام طريقة أكثر موثوقية
        current_pos = 0
        try:
            # استخدام الوقت المنقضي كطريقة أساسية (أكثر موثوقية)
            if hasattr(self, 'play_start_time'):
                current_pos = time.time() - self.play_start_time

                # تصحيح الموضع إذا كان سالبًا
                if current_pos < 0:
                    current_pos = 0
                    # إعادة ضبط وقت البدء
                    self.play_start_time = time.time()

                # محاولة استخدام get_pos كتحقق إضافي فقط إذا كان متاحًا
                if hasattr(self.sound, 'get_pos'):
                    try:
                        pos_from_sound = self.sound.get_pos()
                        # استخدام قيمة get_pos فقط إذا كانت معقولة
                        if pos_from_sound > 0 and abs(pos_from_sound - current_pos) > 5:
                            # إذا كان الفرق كبيرًا، قم بتحديث وقت البدء
                            self.play_start_time = time.time() - pos_from_sound
                            current_pos = pos_from_sound
                    except Exception as pos_error:
                        # تجاهل أخطاء get_pos والاستمرار باستخدام الوقت المنقضي
                        pass
            elif hasattr(self.sound, 'get_pos'):
                # إذا لم يكن وقت البدء متاحًا، استخدم get_pos
                try:
                    current_pos = self.sound.get_pos()
                    if current_pos < 0:
                        current_pos = 0
                except:
                    current_pos = 0
        except Exception as pos_error:
            # استخدام القيمة المخزنة سابقًا أو 0
            current_pos = getattr(self, 'current_pos', 0)
            print(f"Error getting position: {pos_error}")

        # تخزين الموضع الحالي للاستئناف لاحقًا
        self.current_pos = current_pos

        # تحديث شريط التقدم الدائري في الشريط السفلي (فقط إذا كان التحديث مطلوبًا)
        if update_ui:
            try:
                if hasattr(self.ids, 'play_progress_main'):
                    # تعيين القيمة القصوى (فقط إذا تغيرت)
                    if hasattr(self.sound, 'length') and self.sound.length > 0:
                        if self.ids.play_progress_main.max != self.sound.length:
                            self.ids.play_progress_main.max = self.sound.length

                    # تحديث القيمة وإجبار إعادة الرسم (فقط إذا تغيرت بشكل ملحوظ)
                    if abs(self.ids.play_progress_main.value - current_pos) > 0.5:
                        self.ids.play_progress_main.value = current_pos
                        # استدعاء draw فقط إذا كان ضروريًا (لتحسين الأداء)
                        if hasattr(self.ids.play_progress_main, 'draw'):
                            self.ids.play_progress_main.draw()
            except Exception as circular_error:
                # تسجيل الخطأ ولكن لا تظهره في السجل في كل مرة (لتقليل الضوضاء)
                if not hasattr(self, '_last_circular_error_time') or time.time() - self._last_circular_error_time > 60:
                    logger.error(f"Error updating circular progress bar: {circular_error}")
                    self._last_circular_error_time = time.time()

        # تحديث وقت الأغنية في الشريط السفلي (فقط إذا كان التحديث مطلوبًا)
        if update_ui and hasattr(self.ids, 'current_time_main'):
            try:
                # تحديث الوقت الحالي في الشريط السفلي (فقط إذا تغير)
                formatted_time = self.format_time(current_pos)
                if self.ids.current_time_main.text != formatted_time:
                    self.ids.current_time_main.text = formatted_time

                # تحديث الوقت الإجمالي في الشريط السفلي (فقط إذا تغير وكان متاحًا)
                if hasattr(self.sound, 'length') and hasattr(self.ids, 'total_time_main'):
                    formatted_total = self.format_time(self.sound.length)
                    if self.ids.total_time_main.text != formatted_total:
                        self.ids.total_time_main.text = formatted_total
            except Exception as time_error:
                # تسجيل الخطأ ولكن لا تظهره في السجل في كل مرة
                if not hasattr(self, '_last_time_error_time') or time.time() - self._last_time_error_time > 60:
                    logger.error(f"Error updating time labels: {time_error}")
                    self._last_time_error_time = time.time()

        # تحديث واجهة المستخدم في شاشة التشغيل الآن (فقط إذا كانت مفتوحة وكان التحديث مطلوبًا)
        if update_ui and hasattr(self.ids, 'screen_manager') and self.ids.screen_manager.current == 'now_playing':
            # تحديث تسميات الوقت (فقط إذا تغيرت)
            try:
                if hasattr(self.ids, 'current_time_now_playing'):
                    formatted_time = self.format_time(current_pos)
                    if self.ids.current_time_now_playing.text != formatted_time:
                        self.ids.current_time_now_playing.text = formatted_time

                if hasattr(self.ids, 'total_time_now_playing') and hasattr(self.sound, 'length'):
                    formatted_total = self.format_time(self.sound.length)
                    if self.ids.total_time_now_playing.text != formatted_total:
                        self.ids.total_time_now_playing.text = formatted_total
            except Exception as time_error:
                # تسجيل الخطأ ولكن لا تظهره في السجل في كل مرة
                pass

            # تحديث شريط التقدم المخصص (فقط إذا كان التحديث مطلوبًا)
            try:
                # تخزين مرجع لشريط التقدم المخصص للتحديثات المستقبلية (للأداء الأفضل)
                if not hasattr(self, '_cached_seek_slider'):
                    # استيراد CustomSlider فقط إذا كان ضروريًا
                    try:
                        from custom_slider import CustomSlider
                    except ImportError:
                        # تسجيل التحذير مرة واحدة فقط
                        if not hasattr(self, '_custom_slider_import_warning'):
                            logger.warning("Could not import CustomSlider")
                            self._custom_slider_import_warning = True
                        return

                    # البحث عن الويدجت بطريقة آمنة (مرة واحدة فقط)
                    found_slider = None
                    for widget in self.walk(restrict=True):
                        if isinstance(widget, CustomSlider) and hasattr(widget, 'id') and widget.id == 'seek_slider_now_playing':
                            found_slider = widget
                            break

                    # تخزين المرجع للاستخدام المستقبلي
                    self._cached_seek_slider = found_slider

                # استخدام المرجع المخزن
                if hasattr(self, '_cached_seek_slider') and self._cached_seek_slider:
                    # تحديث نص الوقت (فقط إذا تغير)
                    if hasattr(self._cached_seek_slider, 'time_text') and self._cached_seek_slider.time_text != self.format_time(current_pos):
                        self._cached_seek_slider.time_text = self.format_time(current_pos)

                    # تعيين القيمة القصوى (فقط إذا تغيرت)
                    if hasattr(self.sound, 'length') and self.sound.length > 0 and hasattr(self._cached_seek_slider, 'max'):
                        if self._cached_seek_slider.max != self.sound.length:
                            self._cached_seek_slider.max = self.sound.length

                    # تحديث القيمة (فقط إذا لم يكن المستخدم يبحث وتغيرت القيمة بشكل ملحوظ)
                    if hasattr(self._cached_seek_slider, 'is_seeking') and hasattr(self._cached_seek_slider, 'value'):
                        if not self._cached_seek_slider.is_seeking and abs(self._cached_seek_slider.value - current_pos) > 0.5:
                            self._cached_seek_slider.value = current_pos
            except Exception as slider_error:
                # تسجيل الخطأ ولكن لا تظهره في السجل في كل مرة
                if not hasattr(self, '_last_slider_error_time') or time.time() - self._last_slider_error_time > 60:
                    logger.error(f"Error updating progress bar in UI: {slider_error}")
                    self._last_slider_error_time = time.time()

                # إعادة تعيين المرجع المخزن في حالة حدوث خطأ
                if hasattr(self, '_cached_seek_slider'):
                    delattr(self, '_cached_seek_slider')

        # التحقق مما إذا كان المسار قد انتهى (دائمًا، بغض النظر عن التحديث)
        try:
            if hasattr(self.sound, 'length') and self.sound.length > 0:
                # تحقق من أن الموضع الحالي قريب من نهاية المسار
                if current_pos >= self.sound.length - 0.5:
                    # إيقاف الصوت الحالي لتجنب التداخل
                    if hasattr(self.sound, 'stop'):
                        self.sound.stop()
                    # استدعاء دالة انتهاء المسار
                    self.on_track_finish()
        except Exception as finish_error:
            # تسجيل الخطأ ولكن لا تظهره في السجل في كل مرة
            pass

    def seek_to_position(self, position):
        """Seek to a specific position in the current track (legacy method)"""
        # Esta función es una duplicada y no debería usarse.
        # Llamamos a la implementación principal
        logger.warning("seek_to_position is deprecated, use seek_to instead")
        try:
            # التحقق من وجود كائن الصوت
            if not hasattr(self, 'sound') or self.sound is None:
                logger.warning("لا يوجد مسار للانتقال إليه")
                return False

            # تعيين علامة البحث
            self.user_seeking = True

            # التحقق من وجود خاصية length
            if not hasattr(self.sound, 'length'):
                logger.warning("كائن الصوت لا يحتوي على خاصية length")
                self.user_seeking = False
                return False

            # التأكد من أن القيمة ضمن النطاق الصحيح
            try:
                max_position = self.sound.length
                if max_position <= 0:
                    max_position = 100  # قيمة افتراضية إذا كان الطول غير صالح
                position = max(0, min(position, max_position))
            except Exception as length_error:
                logger.error(f"خطأ في الحصول على طول المسار: {length_error}")
                position = max(0, min(position, 100))  # قيمة افتراضية

            # التحقق من وجود دالة seek
            if not hasattr(self.sound, 'seek'):
                logger.warning("كائن الصوت لا يحتوي على دالة seek")
                self.user_seeking = False
                return False

            # الانتقال إلى الموضع الجديد
            try:
                self.sound.seek(position)
                logger.info(f"تم الانتقال إلى الموضع: {position}")
            except Exception as seek_error:
                logger.error(f"خطأ في الانتقال إلى الموضع: {seek_error}")
                self.user_seeking = False
                return False

            # تحديث عرض الوقت
            if self.ids.screen_manager.current == 'now_playing':
                if hasattr(self.ids, 'current_time_now_playing'):
                    self.ids.current_time_now_playing.text = self.format_time(position)

            # تحديث شريط التقدم مباشرة
            if hasattr(self.ids, 'progress_bar'):
                self.ids.progress_bar.value = position

            # تحديث شريط التقدم الدائري
            if hasattr(self.ids, 'play_progress_main'):
                self.ids.play_progress_main.value = position
                self.ids.play_progress_main.draw()

            # تحديث الموضع الحالي
            self.current_pos = position

            # إعادة تعيين علامة البحث بعد تأخير قصير
            Clock.schedule_once(lambda dt: setattr(self, 'user_seeking', False), 0.1)
            return True
        except Exception as e:
            logger.error(f"خطأ في seek_to_position: {e}")
            self.user_seeking = False
            return False

    # These methods are no longer needed with our new progress bar
    def start_seek(self, *args):
        pass

    def end_seek(self, *args):
        pass

    def on_seek(self, value):
        pass

    def format_time(self, seconds):
        try:
            if seconds is None or seconds < 0:
                return "00:00"
            minutes, seconds = divmod(seconds, 60)
            return f"{int(minutes):02}:{int(seconds):02}"
        except Exception as e:
            logger.error(f"Error in format_time: {e}")
            return "00:00"

    def get_track_length(self):
        """Get the total length of the current track in seconds"""
        if self.sound and hasattr(self.sound, 'length'):
            return self.sound.length
        return 100  # Default value if no track is loaded

    def get_text_color(self):
        app = MDApp.get_running_app()
        if app.theme_cls.theme_style == "Dark":
            return [1, 1, 1, 1]
        else:
            return [0.13, 0.13, 0.13, 1]

    def get_bg_color(self):
        app = MDApp.get_running_app()
        if app.theme_cls.theme_style == "Dark":
            return [0.12, 0.12, 0.12, 1]
        else:
            return [0.95, 0.95, 0.95, 1]

    def get_gradient_bg_colors(self):
        """إرجاع ألوان متدرجة للخلفية بناءً على الثيم الحالي"""
        app = MDApp.get_running_app()
        primary_color = app.theme_cls.primary_color

        if app.theme_cls.theme_style == "Dark":
            # خلفية متدرجة داكنة
            color1 = [0.08, 0.08, 0.12, 1]  # أزرق داكن جداً
            color2 = [0.12, 0.12, 0.18, 1]  # أزرق داكن
            color3 = [primary_color[0] * 0.1, primary_color[1] * 0.1, primary_color[2] * 0.1, 1]
        else:
            # خلفية متدرجة فاتحة
            color1 = [0.98, 0.98, 1.0, 1]   # أبيض مزرق
            color2 = [0.94, 0.96, 0.98, 1]  # رمادي فاتح مزرق
            color3 = [primary_color[0] * 0.9 + 0.1, primary_color[1] * 0.9 + 0.1, primary_color[2] * 0.9 + 0.1, 1]

        return [color1, color2, color3]

    def get_dynamic_bg_color(self):
        """إرجاع لون خلفية ديناميكي بناءً على الأغنية الحالية"""
        try:
            # إذا كانت هناك أغنية قيد التشغيل، استخدم لون مشتق من غلاف الألبوم
            if self.current_index >= 0 and self.current_index < len(self.playlist):
                # استخدام لون أساسي مع شفافية
                app = MDApp.get_running_app()
                primary_color = app.theme_cls.primary_color

                if app.theme_cls.theme_style == "Dark":
                    return [primary_color[0] * 0.15, primary_color[1] * 0.15, primary_color[2] * 0.15, 1]
                else:
                    return [primary_color[0] * 0.95 + 0.05, primary_color[1] * 0.95 + 0.05, primary_color[2] * 0.95 + 0.05, 1]
            else:
                return self.get_bg_color()
        except Exception as e:
            logger.error(f"Error getting dynamic background color: {e}")
            return self.get_bg_color()

    def get_pattern_bg_color(self):
        """إرجاع لون خلفية مع نمط للقائمة"""
        app = MDApp.get_running_app()
        primary_color = app.theme_cls.primary_color

        if app.theme_cls.theme_style == "Dark":
            # نمط داكن مع لمسة من اللون الأساسي
            base_color = [0.10, 0.10, 0.14, 1]
            accent_color = [primary_color[0] * 0.2, primary_color[1] * 0.2, primary_color[2] * 0.2, 0.3]
        else:
            # نمط فاتح مع لمسة من اللون الأساسي
            base_color = [0.97, 0.97, 0.99, 1]
            accent_color = [primary_color[0] * 0.8 + 0.2, primary_color[1] * 0.8 + 0.2, primary_color[2] * 0.8 + 0.2, 0.1]

        return {'base': base_color, 'accent': accent_color}

    def update_background_theme(self):
        """تحديث خلفية التطبيق بناءً على الثيم والأغنية الحالية"""
        try:
            # تطبيق الخلفية المحسنة مباشرة
            self.apply_enhanced_background()
            logger.info("Background theme updated successfully")

        except Exception as e:
            logger.error(f"Error updating background theme: {e}")

    def apply_background_update(self, gradient_colors, pattern_colors):
        """تطبيق تحديث الخلفية على واجهة المستخدم"""
        try:
            # البحث عن عنصر الخلفية في قائمة الأغاني
            playlist_list = self.ids.get('playlist_list')
            if playlist_list:
                # إضافة خلفية متدرجة إذا لم تكن موجودة
                background_widget = None
                for child in playlist_list.children:
                    if isinstance(child, (GradientBackground, PatternBackground)):
                        background_widget = child
                        break

                if not background_widget:
                    # إنشاء خلفية جديدة
                    background_widget = GradientBackground()
                    playlist_list.add_widget(background_widget, index=len(playlist_list.children))

                # تحديث ألوان الخلفية
                if isinstance(background_widget, GradientBackground):
                    background_widget.colors = gradient_colors[:2]  # استخدام أول لونين
                    background_widget.direction = 'vertical'

                logger.info("Background updated successfully")

        except Exception as e:
            logger.error(f"Error applying background update: {e}")

    def set_background_style(self, style='gradient'):
        """تعيين نمط الخلفية"""
        try:
            playlist_list = self.ids.get('playlist_list')
            if not playlist_list:
                return

            # إزالة الخلفيات الموجودة
            for child in list(playlist_list.children):
                if isinstance(child, (GradientBackground, PatternBackground)):
                    playlist_list.remove_widget(child)

            # إضافة الخلفية الجديدة
            if style == 'gradient':
                gradient_colors = self.get_gradient_bg_colors()
                background = GradientBackground(
                    colors=gradient_colors[:2],
                    direction='vertical'
                )
            elif style == 'pattern':
                pattern_colors = self.get_pattern_bg_color()
                background = PatternBackground(
                    base_color=pattern_colors['base'],
                    pattern_color=pattern_colors['accent'],
                    pattern_type='dots'
                )
            else:
                # خلفية عادية
                return

            # إضافة الخلفية الجديدة
            playlist_list.add_widget(background, index=len(playlist_list.children))

            logger.info(f"Background style set to: {style}")

        except Exception as e:
            logger.error(f"Error setting background style: {e}")

    def setup_enhanced_background(self, dt):
        """إعداد الخلفية المحسنة عند بدء التطبيق"""
        try:
            logger.info("Setting up enhanced background...")

            # تطبيق الخلفية المحسنة مباشرة
            self.apply_enhanced_background()

            # ربط تحديث الخلفية بتغيير الثيم
            self.bind(theme_name=self.on_theme_changed)

            logger.info("Enhanced background setup completed")

        except Exception as e:
            logger.error(f"Error setting up enhanced background: {e}")

    def apply_enhanced_background(self):
        """تطبيق الخلفية المحسنة مباشرة على قائمة الأغاني"""
        try:
            playlist_list = self.ids.get('playlist_list')
            if not playlist_list:
                logger.warning("playlist_list not found")
                return

            # مسح الخلفية الحالية
            playlist_list.canvas.before.clear()

            # الحصول على الألوان
            bg_color = self.get_dynamic_bg_color()
            primary_color = self.get_primary_color()

            # إنشاء خلفية متدرجة بسيطة
            with playlist_list.canvas.before:
                from kivy.graphics import Color, Rectangle, Line

                # الخلفية الأساسية
                Color(*bg_color)
                Rectangle(pos=playlist_list.pos, size=playlist_list.size)

                # تأثير متدرج علوي
                Color(primary_color[0], primary_color[1], primary_color[2], 0.1)
                Rectangle(
                    pos=(playlist_list.x, playlist_list.y + playlist_list.height * 0.7),
                    size=(playlist_list.width, playlist_list.height * 0.3)
                )

                # خطوط زخرفية خفيفة
                Color(primary_color[0], primary_color[1], primary_color[2], 0.05)
                for i in range(3):
                    y_pos = playlist_list.y + playlist_list.height * (0.25 + i * 0.25)
                    Line(
                        points=[playlist_list.x, y_pos, playlist_list.x + playlist_list.width, y_pos],
                        width=1
                    )

            # ربط تحديث الخلفية بتغيير الحجم والموضع
            playlist_list.bind(pos=self.update_background_canvas, size=self.update_background_canvas)

            logger.info("Enhanced background applied successfully")

        except Exception as e:
            logger.error(f"Error applying enhanced background: {e}")

    def update_background_canvas(self, instance, value):
        """تحديث خلفية Canvas عند تغيير الحجم أو الموضع"""
        try:
            Clock.schedule_once(lambda dt: self.apply_enhanced_background(), 0.1)
        except Exception as e:
            logger.error(f"Error updating background canvas: {e}")

    def on_theme_changed(self, instance, theme_name):
        """معالج تغيير الثيم لتحديث الخلفية"""
        try:
            logger.info(f"Theme changed to: {theme_name}")

            # تحديث الخلفية بناءً على الثيم الجديد
            Clock.schedule_once(lambda dt: self.update_background_theme(), 0.2)

        except Exception as e:
            logger.error(f"Error handling theme change: {e}")

    def cycle_background_style(self):
        """تبديل نمط الخلفية بين الأنماط المختلفة"""
        try:
            # قائمة الأنماط المتاحة
            styles = ['normal', 'enhanced', 'minimal']

            # الحصول على النمط الحالي
            current_style = getattr(self, '_current_bg_style', 'enhanced')

            # الانتقال إلى النمط التالي
            current_index = styles.index(current_style) if current_style in styles else 1
            next_index = (current_index + 1) % len(styles)
            next_style = styles[next_index]

            # تطبيق النمط الجديد
            self._current_bg_style = next_style

            if next_style == 'enhanced':
                self.apply_enhanced_background()
                style_name = "خلفية محسنة"
            elif next_style == 'minimal':
                self.apply_minimal_background()
                style_name = "خلفية بسيطة"
            else:
                self.apply_normal_background()
                style_name = "خلفية عادية"

            logger.info(f"Background style cycled to: {next_style}")

            # إظهار رسالة للمستخدم
            from kivymd.toast import toast
            toast(f"نمط الخلفية: {style_name}")

        except Exception as e:
            logger.error(f"Error cycling background style: {e}")

    def apply_minimal_background(self):
        """تطبيق خلفية بسيطة"""
        try:
            playlist_list = self.ids.get('playlist_list')
            if not playlist_list:
                return

            playlist_list.canvas.before.clear()

            with playlist_list.canvas.before:
                from kivy.graphics import Color, Rectangle

                # خلفية بسيطة بلون واحد
                bg_color = self.get_bg_color()
                Color(*bg_color)
                Rectangle(pos=playlist_list.pos, size=playlist_list.size)

            logger.info("Minimal background applied")

        except Exception as e:
            logger.error(f"Error applying minimal background: {e}")

    def apply_normal_background(self):
        """تطبيق خلفية عادية"""
        try:
            playlist_list = self.ids.get('playlist_list')
            if not playlist_list:
                return

            playlist_list.canvas.before.clear()

            with playlist_list.canvas.before:
                from kivy.graphics import Color, Rectangle

                # خلفية عادية مع لمسة من اللون الأساسي
                bg_color = self.get_bg_color()
                primary_color = self.get_primary_color()

                Color(*bg_color)
                Rectangle(pos=playlist_list.pos, size=playlist_list.size)

                # إضافة لمسة خفيفة من اللون الأساسي
                Color(primary_color[0], primary_color[1], primary_color[2], 0.05)
                Rectangle(pos=playlist_list.pos, size=playlist_list.size)

            logger.info("Normal background applied")

        except Exception as e:
            logger.error(f"Error applying normal background: {e}")

    def get_card_bg_color(self):
        app = MDApp.get_running_app()
        if app.theme_cls.theme_style == "Dark":
            return [0.18, 0.18, 0.18, 1]
        else:
            return [1, 1, 1, 1]

    def get_button_bg_color(self):
        app = MDApp.get_running_app()
        if app.theme_cls.theme_style == "Dark":
            return [0.25, 0.25, 0.25, 1]
        else:
            return [0.9, 0.9, 0.9, 1]

    def get_primary_color(self):
        app = MDApp.get_running_app()
        return app.theme_cls.primary_color

    def get_error_color(self):
        app = MDApp.get_running_app()
        return app.theme_cls.error_color

    def start_song_title_animation(self):
        """بدء تحريك عنوان الأغنية إذا كان طويلاً"""
        try:
            if not hasattr(self.ids, 'now_playing_track_name'):
                return

            label = self.ids.now_playing_track_name

            # إيقاف أي تحريك سابق
            Animation.stop_all(label)

            # إعادة تعيين الموضع
            label.anim_offset = 0

            # تحديث النسيج لحساب العرض
            label.texture_update()

            # التحقق مما إذا كان النص أطول من العرض المتاح
            if label.texture_size[0] > label.width:
                # تحديد اتجاه التحريك بناءً على لغة النص
                is_arabic = any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in label.text)

                # حساب المسافة والمدة
                distance = label.texture_size[0] - label.width
                duration = max(5, distance / 30)  # سرعة معقولة

                if is_arabic:
                    # للنص العربي، التحريك من اليمين إلى اليسار
                    anim1 = Animation(anim_offset=-distance, duration=duration)
                    anim2 = Animation(anim_offset=0, duration=duration)
                else:
                    # للنص الإنجليزي، التحريك من اليسار إلى اليمين
                    anim1 = Animation(anim_offset=-distance, duration=duration)
                    anim2 = Animation(anim_offset=0, duration=duration)

                # إضافة توقف في البداية والنهاية
                anim1.bind(on_complete=lambda *args: Clock.schedule_once(
                    lambda dt: anim2.start(label), 1.5))
                anim2.bind(on_complete=lambda *args: Clock.schedule_once(
                    lambda dt: anim1.start(label), 1.5))

                # بدء التحريك بعد تأخير قصير
                Clock.schedule_once(lambda dt: anim1.start(label), 2)

                # تعيين علامة التحريك
                label.is_animating = True
            else:
                # إذا كان النص قصيرًا، لا داعي للتحريك
                label.is_animating = False
        except Exception as e:
            logger.error(f"خطأ في تحريك عنوان الأغنية: {e}")

    def toggle_theme(self):
        self.cycle_theme()

    def cycle_theme(self):
        try:
            themes = [
                "DeepPurple", "Blue", "Indigo", "Teal", "Green",
                "LightGreen", "Lime", "Yellow", "Amber", "Orange",
                "Red", "Pink", "Purple", "BlueGray", "Gray", "Brown"
            ]

            try:
                current_index = themes.index(self.theme_name)
            except ValueError:
                current_index = 0

            next_index = (current_index + 1) % len(themes)
            next_theme = themes[next_index]

            self.apply_theme(next_theme)

            from kivymd.toast import toast
            toast(f"Theme changed to {next_theme}")
        except Exception as e:
            logger.error(f"Error in cycle_theme: {e}")

    def show_theme_selection_dialog(self):
        themes = [
            {"name": "DeepPurple", "display": "Deep Purple", "color": [0.4, 0.2, 0.6, 1]},
            {"name": "Teal", "display": "Teal", "color": [0, 0.6, 0.6, 1]},
            {"name": "Indigo", "display": "Indigo", "color": [0.25, 0.31, 0.71, 1]},
            {"name": "BlueGray", "display": "Blue Gray", "color": [0.38, 0.49, 0.55, 1]},
            {"name": "Orange", "display": "Orange", "color": [1, 0.6, 0, 1]},
            {"name": "Red", "display": "Red", "color": [0.9, 0.2, 0.2, 1]},
            {"name": "Pink", "display": "Pink", "color": [0.91, 0.12, 0.39, 1]},
            {"name": "Purple", "display": "Purple", "color": [0.55, 0.14, 0.59, 1]},
            {"name": "Blue", "display": "Blue", "color": [0.13, 0.59, 0.95, 1]},
            {"name": "LightBlue", "display": "Light Blue", "color": [0.01, 0.66, 0.96, 1]},
            {"name": "Cyan", "display": "Cyan", "color": [0, 0.74, 0.83, 1]},
            {"name": "Green", "display": "Green", "color": [0.3, 0.69, 0.31, 1]},
            {"name": "LightGreen", "display": "Light Green", "color": [0.55, 0.76, 0.29, 1]},
            {"name": "Lime", "display": "Lime", "color": [0.8, 0.86, 0.22, 1]},
            {"name": "Yellow", "display": "Yellow", "color": [1, 0.92, 0.23, 1]},
            {"name": "Amber", "display": "Amber", "color": [1, 0.76, 0.03, 1]},
            {"name": "Brown", "display": "Brown", "color": [0.47, 0.33, 0.28, 1]},
            {"name": "Gray", "display": "Gray", "color": [0.62, 0.62, 0.62, 1]}
        ]

        class ThemeItem(OneLineAvatarIconListItem):
            divider = None

            def __init__(self, theme_data, **kwargs):
                super().__init__(**kwargs)
                self.theme_data = theme_data
                self.text = theme_data["display"]

                # Use the imported ColorCircle class
                color_circle = ColorCircle(color=theme_data["color"])
                self.add_widget(color_circle)

            def __getattr__(self, name):
                """Handle attribute access for compatibility with super().__getattr__"""
                # This prevents 'super' object has no attribute '__getattr__' error
                raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

        theme_items = []
        for theme in themes:
            def make_select_callback(theme_name):
                return lambda x: self.apply_theme(theme_name)

            item = ThemeItem(
                theme_data=theme,
                on_release=make_select_callback(theme["name"]),
                font_name=self.current_font
            )
            theme_items.append(item)

        dialog = MDDialog(
            title="Select Theme",
            type="simple",
            items=theme_items,
            buttons=[
                MDFlatButton(
                    text="CANCEL",
                    theme_text_color="Custom",
                    text_color=self.get_primary_color(),
                    on_release=lambda x: dialog.dismiss(),
                    font_name=self.current_font
                )
            ]
        )

        self._theme_dialog = dialog
        dialog.open()

    def save_theme(self):
        try:
            # Skip saving if no write permission on Android
            if platform == 'android' and not check_permission(Permission.WRITE_EXTERNAL_STORAGE):
                logger.warning("No write permission—skipping save_theme")
                return

            path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "theme.json")
            with open(path, "w", encoding="utf-8") as file:
                json.dump({"theme_name": self.theme_name}, file)
        except Exception as e:
            logger.error(f"Error saving theme: {e}")

    def load_theme(self):
        try:
            theme_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "theme.json")
            if os.path.exists(theme_path):
                with open(theme_path, "r", encoding="utf-8") as file:
                    data = json.load(file)
                self.theme_name = data.get("theme_name", self.theme_name)
                app = MDApp.get_running_app()
                if app:
                    app.theme_cls.primary_palette = self.theme_name
                self.update_theme()
        except Exception as e:
            logger.error(f"Error loading theme: {e}")

    def apply_theme(self, theme_name):
        try:
            self.theme_name = theme_name
            app = MDApp.get_running_app()
            app.theme_cls.primary_palette = self.theme_name
            self.update_theme()
            self.save_theme()

            # تحديث الخلفية عند تغيير الثيم
            Clock.schedule_once(lambda dt: self.update_background_theme(), 0.3)

            if hasattr(self, '_theme_dialog') and self._theme_dialog:
                self._theme_dialog.dismiss()
                self._theme_dialog = None
        except Exception as e:
            logger.error(f"Error applying theme: {e}")

    def update_theme(self):
        try:
            app = MDApp.get_running_app()
            app.theme_cls.primary_palette = self.theme_name
            for child in self.ids.playlist_list.children:
                child.text_color = self.get_text_color()

            # Progress bar in main screen removed
            self.ids.nav_drawer.md_bg_color = self.get_bg_color()
            self.ids.bottom_bar.md_bg_color = self.get_primary_color()
        except Exception as e:
            logger.error(f"Error in update_theme: {e}")

    def show_settings_menu(self, button):
        """Muestra el menú de configuración general"""
        try:
            menu_items = [
                {
                    "text": "Audio Settings",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x: self.show_audio_settings(),
                    "height": dp(48),
                    "font_name": "Roboto"
                },
                {
                    "text": "Change Theme",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x: self.show_theme_dialog(),
                    "height": dp(48),
                    "font_name": "Roboto"
                }
            ]

            self.settings_menu = MDDropdownMenu(
                caller=button,
                items=menu_items,
                width_mult=3,
                max_height=dp(200),
                background_color=self.get_bg_color(),
                radius=[dp(10), dp(10), dp(10), dp(10)],
                elevation=4
            )

            self.settings_menu.open()
        except Exception as e:
            logger.error(f"Error mostrando menú de configuración: {e}")

    def update_top_bar_buttons(self, dt=None):
        """Actualiza los botones de la barra superior para eliminar los botones de favoritos y descargas"""
        try:
            if hasattr(self.ids, 'top_app_bar'):
                self.ids.top_app_bar.right_action_items = [
                    ["web", lambda x: self.show_search_screen()],
                    ["magnify", lambda x: self.toggle_search()]
                ]
                logger.info("Botones de la barra superior actualizados correctamente")
        except Exception as e:
            logger.error(f"Error al actualizar los botones de la barra superior: {e}")

    def toggle_search(self):
        try:
            self.is_search_visible = not self.is_search_visible
            self.ids.top_app_bar.right_action_items = [
                ["web", lambda x: self.show_search_screen()],
                ["magnify", lambda x: self.toggle_search()],
                *([["search", lambda x: self.perform_search()], ["close", lambda x: self.toggle_search()]] if self.is_search_visible else [])
            ]
            if not self.is_search_visible:
                self.update_playlist_ui()
        except Exception as e:
            logger.error(f"Error in toggle_search: {e}")

    def search_tracks(self, query):
        """Store the search query and handle Arabic text if needed"""
        try:
            # Process Arabic text if needed
            if self.contains_arabic(query):
                # Try to import Arabic text utilities if available
                try:
                    from arabic_utils import reshape_arabic_text, get_display_text
                    # Apply bidirectional algorithm to the query
                    processed_query = get_display_text(query)
                    logger.debug(f"Processed Arabic search query: {processed_query}")
                    self.pending_search_query = processed_query
                except ImportError:
                    # If Arabic utilities are not available, use the query as is
                    logger.warning("Arabic text utilities not available for search")
                    self.pending_search_query = query
            else:
                self.pending_search_query = query

            # If search field is visible and has focus, perform search immediately
            if self.is_search_visible and hasattr(self.ids, 'search_field') and self.ids.search_field.focus:
                Clock.schedule_once(lambda dt: self.perform_search(), 0.1)
        except Exception as e:
            logger.error(f"Error in search_tracks: {e}")
            self.pending_search_query = query

    def perform_search(self):
        """Search for tracks in the playlist based on the query"""
        try:
            query = self.pending_search_query.strip()
            self.ids.playlist_list.clear_widgets()

            if query:
                # Case-insensitive search that works with both Arabic and non-Arabic text
                filtered_playlist = []
                for path in self.playlist:
                    song_title = self.get_song_title(path)

                    # Handle Arabic text comparison
                    if self.contains_arabic(query) or self.contains_arabic(song_title):
                        # For Arabic text, we need to do a more flexible comparison
                        # since the display text might be different from the stored text
                        if query.lower() in song_title.lower() or song_title.lower() in query.lower():
                            filtered_playlist.append(path)
                    else:
                        # For non-Arabic text, do a simple case-insensitive comparison
                        if query.lower() in song_title.lower():
                            filtered_playlist.append(path)
            else:
                filtered_playlist = self.playlist

            for index, path in enumerate(filtered_playlist):
                def make_play_callback(idx):
                    return lambda x: self.play_track_by_index(idx)

                def make_favorite_callback(p):
                    return lambda x: self.toggle_favorite(p)

                def make_delete_callback(p):
                    return lambda x: self.show_delete_confirmation(p)

                song_title = self.get_song_title(path)
                item = LongPressSongItem(
                    text=f"{index + 1}. {song_title}",
                    theme_text_color="Custom",
                    file_path=path,
                    on_release=make_play_callback(index),
                    font_name=self.current_font,
                    index=index  # إضافة خاصية index
                )

                if index == self.current_index:
                    item.text_color = [0, 1, 0, 1]
                else:
                    item.text_color = self.get_text_color()
                item.add_widget(LeftContainer(text=str(index + 1), font_name=self.current_font))

                # إضافة زر إعدادات الأغنية
                def make_settings_callback(p, btn):
                    return lambda x: self.show_song_settings_menu(btn, p)

                settings_btn = RightContainer(
                    icon="dots-vertical",
                    theme_text_color="Custom",
                    text_color=self.get_text_color()
                )
                # تخزين مرجع للزر نفسه لاستخدامه في استدعاء القائمة
                settings_btn.bind(on_release=make_settings_callback(path, settings_btn))
                item.add_widget(settings_btn)

                # Apply Arabic text handling to the item if needed
                if self.contains_arabic(song_title):
                    # Set text alignment to right for Arabic text
                    item.ids.primary_text.halign = 'right'
                    item.ids.primary_text.font_name = 'NotoNaskhArabic-VariableFont_wght'

                    # Try to apply bidirectional algorithm if available
                    try:
                        from arabic_utils import get_display_text
                        if item.ids.primary_text.text != get_display_text(item.ids.primary_text.text):
                            item.ids.primary_text.text = get_display_text(item.ids.primary_text.text)
                    except ImportError:
                        pass

                self.ids.playlist_list.add_widget(item)

            # Show a message if no results were found
            if not filtered_playlist and query:
                no_results_text = "لا توجد نتائج" if self.contains_arabic(query) else "No results found"

                # Create a label to show no results message
                no_results_label = MDLabel(
                    text=no_results_text,
                    halign='center',
                    theme_text_color="Secondary",
                    font_style="Subtitle1",
                    font_name='NotoNaskhArabic-VariableFont_wght',
                    size_hint_y=None,
                    height=dp(80)
                )
                self.ids.playlist_list.add_widget(no_results_label)

        except Exception as e:
            logger.error(f"Error in perform_search: {e}")

    def show_favorites(self):
        try:
            self.is_favorites_visible = not self.is_favorites_visible

            # Reset current index when switching playlists
            if self.is_favorites_visible and self.current_index != -1:
                # Check if current track exists in the new playlist
                current_path = self.playlist[self.current_index] if self.current_index < len(self.playlist) else None
                if current_path and current_path in self.favorites:
                    self.current_index = self.favorites.index(current_path)
                else:
                    self.current_index = -1
            elif not self.is_favorites_visible and self.current_index != -1:
                # Check if current track exists in the main playlist
                current_path = self.favorites[self.current_index] if self.current_index < len(self.favorites) else None
                if current_path and current_path in self.playlist:
                    self.current_index = self.playlist.index(current_path)
                else:
                    self.current_index = -1

            self.ids.top_app_bar.title = "Favorites" if self.is_favorites_visible else "Music Player"

            # Asegurarse de que los botones de la barra superior sean correctos
            self.update_top_bar_buttons()

            self.update_playlist_ui()
        except Exception as e:
            logger.error(f"Error in show_favorites: {e}")
            self.current_index = -1

    def show_format_error_dialog(self, path):
        try:
            file_ext = os.path.splitext(path)[1].lower()

            # Get more detailed error message based on file extension
            if file_ext in ['.m4a', '.aac']:
                error_message = (
                    f"The file '{os.path.basename(path)}' (AAC format) could not be played.\n\n"
                    f"This may be due to codec limitations. The app will try to convert this file "
                    f"to MP3 format for better compatibility. If conversion fails, try converting "
                    f"the file to MP3 format using an external tool."
                )
            elif file_ext in ['.opus', '.ogg']:
                error_message = (
                    f"The file '{os.path.basename(path)}' (Ogg/Opus format) could not be played.\n\n"
                    f"This format may not be supported on your device. The app will try to convert "
                    f"this file to MP3 format for better compatibility. If conversion fails, try "
                    f"converting the file to MP3 format using an external tool."
                )
            elif file_ext == '.flac':
                error_message = (
                    f"The file '{os.path.basename(path)}' (FLAC format) could not be played.\n\n"
                    f"FLAC is a lossless format that may not be supported on your device. The app "
                    f"will try to convert this file to MP3 format for better compatibility. If "
                    f"conversion fails, try converting the file to MP3 format using an external tool."
                )
            elif file_ext in ['.wma', '.asf']:
                error_message = (
                    f"The file '{os.path.basename(path)}' (Windows Media format) could not be played.\n\n"
                    f"This format has limited support on non-Windows platforms. The app will try to "
                    f"convert this file to MP3 format for better compatibility. If conversion fails, "
                    f"try converting the file to MP3 format using an external tool."
                )
            else:
                error_message = (
                    f"The file '{os.path.basename(path)}' could not be played.\n\n"
                    f"This may be due to an unsupported format or codec. The app will try to convert "
                    f"this file to MP3 format for better compatibility. If conversion fails, try "
                    f"converting the file to MP3 format using an external tool."
                )

            dialog = MDDialog(
                title="Format Error",
                text=error_message,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        theme_text_color="Custom",
                        text_color=self.get_primary_color(),
                        on_release=lambda x: dialog.dismiss(),
                        font_name=self.current_font
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            logger.error(f"Error showing format error dialog: {e}")

    def show_error_dialog(self, message):
        try:
            dialog = MDDialog(
                title="Error",
                text=message,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        theme_text_color="Custom",
                        text_color=self.get_primary_color(),
                        on_release=lambda x: dialog.dismiss(),
                        font_name=self.current_font
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            logger.error(f"Error showing error dialog: {e}")

    def show_info_dialog(self, message):
        """عرض رسالة معلومات للمستخدم"""
        try:
            dialog = MDDialog(
                title="معلومات",
                text=message,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        theme_text_color="Custom",
                        text_color=self.get_primary_color(),
                        on_release=lambda x: dialog.dismiss(),
                        font_name=self.current_font
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            logger.error(f"Error showing info dialog: {e}")

    def on_sound_stop(self, *args):
        """تُستدعى عندما يتوقف الصوت (سواء بسبب الانتهاء أو الخطأ)"""
        try:
            # التحقق مما إذا كان الإيقاف بسبب الإيقاف المؤقت
            if hasattr(self, '_paused_sound_path') and self._paused_sound_path:
                # هذا إيقاف مؤقت متعمد، لا تفعل شيئًا
                logger.info("Sound paused intentionally")
                return

            # التحقق مما إذا كان الصوت قد انتهى بالفعل
            if self.sound and hasattr(self.sound, 'get_pos') and hasattr(self.sound, 'length') and self.sound.get_pos() >= self.sound.length - 0.5:
                self.on_track_finish()
            else:
                # قد يكون هناك خطأ في الصوت
                logger.warning("Sound stopped unexpectedly")
                # محاولة إعادة تشغيل الصوت
                if self.sound and self.is_playing:
                    try:
                        self.sound.play()
                    except Exception as play_error:
                        logger.error(f"Error restarting sound: {play_error}")
                        # الانتقال إلى المسار التالي في حالة الفشل
                        self.next_track()
        except Exception as e:
            logger.error(f"Error in on_sound_stop: {e}")

    def on_track_finish(self):
        """تُستدعى عندما ينتهي تشغيل المسار"""
        try:
            print("Track finished")
            # إيقاف مؤقت التقدم
            self.stop_progress_timer()

            # تحديث حالة التشغيل
            self.is_playing = False

            # تنظيف الملفات المؤقتة لتحسين استخدام الذاكرة
            self.cleanup_temp_files()

            # إذا كان التكرار مفعلاً، قم بتشغيل نفس المسار مرة أخرى
            if self.repeat:
                print("Repeat is enabled, playing same track again")
                # إعادة تعيين المتغيرات
                self.current_pos = 0
                self._paused_position = 0
                self._paused_sound_path = None
                # تشغيل نفس المسار مرة أخرى
                self.play_track_by_index(self.current_index)
            else:
                # وإلا، قم بتشغيل المسار التالي
                print("Playing next track")
                # إعادة تعيين المتغيرات
                self.current_pos = 0
                self._paused_position = 0
                self._paused_sound_path = None
                # تشغيل المسار التالي
                self.next_track()

            # تحديث واجهة المستخدم
            self.fix_play_button()
            self.update_ui_play_state()

        except Exception as e:
            print(f"Error in on_track_finish: {e}")
            logger.error(f"خطأ في on_track_finish: {e}")

    def cleanup_temp_files(self):
        """تنظيف الملفات المؤقتة لتحسين استخدام الذاكرة"""
        try:
            # تنظيف ملف الصوت المؤقت إذا كان موجودًا
            if hasattr(self, '_temp_audio_file') and self._temp_audio_file:
                try:
                    if os.path.exists(self._temp_audio_file):
                        os.remove(self._temp_audio_file)
                        print(f"Removed temporary audio file: {self._temp_audio_file}")
                    self._temp_audio_file = None
                except Exception as e:
                    print(f"Error removing temporary audio file: {e}")

            # تنظيف ملفات مؤقتة أخرى
            try:
                import tempfile
                import glob

                # البحث عن ملفات مؤقتة قديمة في مجلد الملفات المؤقتة
                temp_dir = tempfile.gettempdir()
                # البحث عن ملفات mp3 مؤقتة
                temp_files = glob.glob(os.path.join(temp_dir, "*.mp3"))

                # حذف الملفات المؤقتة القديمة (أقدم من ساعة)
                current_time = time.time()
                for temp_file in temp_files:
                    try:
                        # التحقق من وقت إنشاء الملف
                        file_time = os.path.getctime(temp_file)
                        # حذف الملفات القديمة (أقدم من ساعة)
                        if current_time - file_time > 3600:  # 3600 ثانية = ساعة
                            os.remove(temp_file)
                            print(f"Removed old temporary file: {temp_file}")
                    except Exception as file_error:
                        print(f"Error checking/removing temporary file {temp_file}: {file_error}")
            except Exception as temp_error:
                print(f"Error cleaning up temporary directory: {temp_error}")

            # تنظيف الذاكرة
            try:
                import gc
                # إجبار جامع القمامة على تنظيف الذاكرة
                gc.collect()
            except Exception as gc_error:
                print(f"Error running garbage collection: {gc_error}")

        except Exception as e:
            print(f"Error in cleanup_temp_files: {e}")

    def fix_play_button(self, dt=None):
        """إصلاح زر التشغيل/الإيقاف المؤقت"""
        try:
            # تحقق مما إذا كان التحديث الأخير حدث منذ أقل من 0.2 ثانية (لتجنب التحديثات المتكررة)
            current_time = time.time()
            last_update_time = getattr(self, '_last_button_update_time', 0)

            # للأغاني الأونلاين، نقلل من تكرار التحديثات لتحسين الأداء
            if self.is_online_song and (current_time - last_update_time) < 0.2:
                return

            # تحديث وقت آخر تحديث
            self._last_button_update_time = current_time

            # تخزين الأيقونة الصحيحة
            correct_icon = 'pause' if self.is_playing else 'play'

            # تحديث أيقونة زر التشغيل/الإيقاف المؤقت في الشريط السفلي (فقط إذا تغيرت)
            if hasattr(self.ids, 'play_button'):
                if self.ids.play_button.icon != correct_icon:
                    self.ids.play_button.icon = correct_icon

            # تحديث أيقونة زر التشغيل/الإيقاف المؤقت في شاشة التشغيل الآن (فقط إذا تغيرت)
            if hasattr(self.ids, 'now_playing_play_button'):
                if self.ids.now_playing_play_button.icon != correct_icon:
                    self.ids.now_playing_play_button.icon = correct_icon

            # البحث عن زر التشغيل/الإيقاف في شاشة التشغيل الآن (فقط إذا كانت الشاشة مفتوحة)
            if self.ids.screen_manager.current == 'now_playing':
                # استخدام قائمة مخزنة من الأزرار إذا كانت موجودة
                if not hasattr(self, '_cached_play_buttons'):
                    # تخزين قائمة بأزرار التشغيل/الإيقاف للاستخدام المستقبلي
                    self._cached_play_buttons = []

                    # البحث عن جميع أزرار MDIconButton
                    for widget in self.walk():
                        if isinstance(widget, MDIconButton):
                            # التحقق من الأيقونة
                            if hasattr(widget, 'icon') and ('play' in widget.icon or 'pause' in widget.icon):
                                # إضافة الزر إلى القائمة المخزنة
                                self._cached_play_buttons.append(widget)

                # تحديث الأيقونات في القائمة المخزنة
                for widget in self._cached_play_buttons:
                    if widget.icon != correct_icon:
                        widget.icon = correct_icon
        except Exception as e:
            logger.error(f"خطأ في fix_play_button: {e}")

    def on_enter_now_playing(self):
        """تُستدعى عند الدخول إلى شاشة التشغيل الآن"""
        try:
            # تصحيح زر التشغيل/الإيقاف المؤقت بعد تأخير قصير
            Clock.schedule_once(self.fix_play_button, 0.1)
        except Exception as e:
            logger.error(f"خطأ في on_enter_now_playing: {e}")

    def set_volume(self, value):
        """Set the volume of the current sound"""
        try:
            self.volume = value
            if self.sound:
                self.sound.volume = value
        except Exception as e:
            logger.error(f"Error setting volume: {e}")

    def toggle_mute(self):
        """Toggle between muted and previous volume level"""
        try:
            if self.volume > 0:
                # Store current volume and mute
                self._previous_volume = self.volume
                self.set_volume(0)
            else:
                # Restore previous volume or default to 1.0
                previous = getattr(self, '_previous_volume', 1.0)
                self.set_volume(previous)
        except Exception as e:
            logger.error(f"Error toggling mute: {e}")

    def on_request_close(self, *args, **kwargs):
        try:
            self.stop_background_playback()
            return False
        except Exception as e:
            logger.error(f"Error in on_request_close: {e}")
            return False

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

    def setup_android_notification_handlers(self):
        """إعداد معالجات الإشعارات للأندرويد"""
        try:
            try:
                from jnius import autoclass
            except ImportError:
                logger.warning("pyjnius module not found; Android functionality disabled.")
                return

            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            activity = PythonActivity.mActivity

            try:
                from android.broadcast import BroadcastReceiver
            except ImportError:
                logger.warning("android.broadcast module not found; skipping Android broadcast receiver setup.")
                return

            class MusicControlReceiver(BroadcastReceiver):
                def __init__(self, music_player):
                    super(MusicControlReceiver, self).__init__()
                    self.music_player = music_player

                def onReceive(self, context, intent):
                    try:
                        action = intent.getAction()
                        if action == "ACTION_PLAY_PAUSE":
                            self.music_player.toggle_play_only()
                        elif action == "ACTION_NEXT":
                            self.music_player.next_track()
                        elif action == "ACTION_PREVIOUS":
                            self.music_player.prev_track()
                    except Exception as e:
                        logger.error(f"Error in MusicControlReceiver: {e}")

            self.receiver = MusicControlReceiver(self)
            self.receiver.register()

            logger.info("Android notification handlers set up successfully")
        except Exception as e:
            logger.error(f"Error setting up Android notification handlers: {e}")

    def bottom_bar_touch_down(self, instance, touch):
        try:
            if instance.collide_point(*touch.pos):
                self._bottom_bar_touch_start = touch.y
                self._bottom_bar_touch_pos = touch.pos
                self._bottom_bar_touch_time = Clock.get_time()
                return True
            return False
        except Exception as e:
            logger.error(f"Error in bottom_bar_touch_down: {e}")
            return False

    def bottom_bar_touch_up(self, instance, touch):
        try:
            if instance.collide_point(*touch.pos):
                # التحقق مما إذا كان النقر على زر التشغيل/الإيقاف أو الشريط الدائري
                play_button_clicked = False

                # التحقق مما إذا كان النقر على حاوية زر التشغيل
                if hasattr(self.ids, 'play_button_container'):
                    container = self.ids.play_button_container
                    # تحويل إحداثيات اللمس إلى إحداثيات الحاوية
                    container_pos = container.to_widget(*touch.pos)
                    if container.collide_point(*container_pos):
                        play_button_clicked = True

                # التحقق من الشريط الدائري
                if hasattr(self.ids, 'play_progress_main'):
                    progress_bar = self.ids.play_progress_main
                    # تحويل إحداثيات اللمس إلى إحداثيات الشريط الدائري
                    progress_pos = progress_bar.to_widget(*touch.pos)
                    if progress_bar.collide_point(*progress_pos):
                        play_button_clicked = True

                # إذا لم يتم العثور على الحاوية، نبحث في جميع العناصر الفرعية
                if not play_button_clicked:
                    for widget in instance.walk():
                        # التحقق مما إذا كان الويدجت هو زر التشغيل
                        if isinstance(widget, MDIconButton) and widget.icon in ['play', 'pause']:
                            # التحقق مما إذا كان النقر داخل منطقة الزر الموسعة
                            button_pos = widget.to_widget(*touch.pos)
                            if widget.collide_point(*button_pos):
                                play_button_clicked = True
                                break

                # إذا تم النقر على زر التشغيل أو الشريط الدائري، فقط قم بتبديل حالة التشغيل
                if play_button_clicked:
                    self.toggle_play_only()
                    return True

                # إذا كان النقر على أي جزء آخر من الشريط السفلي، افتح شاشة التشغيل الحالي
                self.show_now_playing()
                return True

            return False
        except Exception as e:
            logger.error(f"Error in bottom_bar_touch_up: {e}")
            return False

    def on_bottom_bar_click(self, instance):
        try:
            if hasattr(instance, 'id'):
                if instance.id == 'play_pause_button':
                    # إذا كان النقر على زر التشغيل/الإيقاف، فقط قم بتبديل حالة التشغيل
                    self.toggle_play_only()
                else:
                    # إذا كان النقر على أي جزء آخر من الشريط السفلي، افتح شاشة التشغيل الحالي
                    self.show_now_playing()
        except Exception as e:
            logger.error(f"Error in on_bottom_bar_click: {e}")

    def show_swipe_notification(self, direction):
        pass

    def show_background_playback_status(self):
        pass

    def update_media_notification(self):
        """تحديث إشعار الوسائط على الأندرويد"""

        try:
            try:
                from jnius import autoclass, cast
            except ImportError:
                logger.warning("pyjnius module not found; cannot update media notification.")
                return

            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            Intent = autoclass('android.content.Intent')
            PendingIntent = autoclass('android.app.PendingIntent')
            NotificationManagerCompat = autoclass('androidx.core.app.NotificationManagerCompat')
            NotificationCompatBuilder = autoclass('androidx.core.app.NotificationCompat$Builder')
            Context = autoclass('android.content.Context')
            NotificationChannel = autoclass('android.app.NotificationChannel')
            NotificationManager = autoclass('android.app.NotificationManager')
            Build = autoclass('android.os.Build')

            activity = PythonActivity.mActivity

            if Build.VERSION.SDK_INT >= Build.VERSION_CODES.O:
                channel_id = "music_player_channel"
                channel_name = "Music Player Controls"
                channel_description = "Media controls for Music Player"
                importance = NotificationManager.IMPORTANCE_LOW

                channel = NotificationChannel(channel_id, channel_name, importance)
                channel.setDescription(channel_description)

                notification_manager = activity.getSystemService(Context.NOTIFICATION_SERVICE)
                notification_manager.createNotificationChannel(channel)
            else:
                channel_id = "music_player_channel"

            playIntent = Intent(activity, PythonActivity)
            playIntent.setAction("ACTION_PLAY_PAUSE")
            playPendingIntent = PendingIntent.getActivity(
                activity, 0, playIntent, PendingIntent.FLAG_UPDATE_CURRENT
            )

            nextIntent = Intent(activity, PythonActivity)
            nextIntent.setAction("ACTION_NEXT")
            nextPendingIntent = PendingIntent.getActivity(
                activity, 1, nextIntent, PendingIntent.FLAG_UPDATE_CURRENT
            )

            prevIntent = Intent(activity, PythonActivity)
            prevIntent.setAction("ACTION_PREVIOUS")
            prevPendingIntent = PendingIntent.getActivity(
                activity, 2, prevIntent, PendingIntent.FLAG_UPDATE_CURRENT
            )

            builder = NotificationCompatBuilder(activity, channel_id)
            builder.setSmallIcon(activity.getApplicationInfo().icon)

            current_song = "No Track" if self.current_index == -1 else self.get_song_title(
                self.favorites[self.current_index] if self.is_favorites_visible else self.playlist[self.current_index]
            )
            builder.setContentTitle("Music Player")
            builder.setContentText("Now Playing: " + current_song)
            builder.setOngoing(self.is_playing)

            builder.addAction(0, "Previous", prevPendingIntent)
            if self.is_playing:
                builder.addAction(0, "Pause", playPendingIntent)
            else:
                builder.addAction(0, "Play", playPendingIntent)
            builder.addAction(0, "Next", nextPendingIntent)

            MediaStyle = autoclass('androidx.media.app.NotificationCompat$MediaStyle')
            style = MediaStyle()
            style.setShowActionsInCompactView(0, 1, 2)
            builder.setStyle(style)

            notificationManager = NotificationManagerCompat.from_(activity)
            notification_id = 1001
            notificationManager.notify(notification_id, builder.build())

        except Exception as e:
            logger.error(f"Error updating media notification: {e}")

    def stop_background_playback(self):
        """إيقاف التشغيل في الخلفية وإزالة الإشعارات"""
        try:
            # التحقق من وجود الصوت قبل محاولة إيقافه
            if hasattr(self, 'sound') and self.sound is not None:
                try:
                    # التحقق من وجود دالة stop
                    if hasattr(self.sound, 'stop'):
                        self.sound.stop()
                        logger.info("Sound stopped successfully")
                    else:
                        logger.warning("Sound object has no stop method")
                except Exception as stop_error:
                    logger.error(f"Error stopping sound: {stop_error}")
            else:
                logger.info("No sound object to stop")

            # تعيين حالة التشغيل إلى False بغض النظر عن وجود الصوت
            self.is_playing = False

            # إيقاف مؤقت التقدم
            if hasattr(self, 'stop_progress_timer'):
                try:
                    self.stop_progress_timer()
                    logger.info("Progress timer stopped successfully")
                except Exception as timer_error:
                    logger.error(f"Error stopping progress timer: {timer_error}")

            # إزالة الإشعارات على أندرويد
            try:
                from jnius import autoclass

                # الحصول على مدير الإشعارات
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                Context = autoclass('android.content.Context')
                NotificationManagerCompat = autoclass('androidx.core.app.NotificationManagerCompat')

                # إزالة الإشعار
                activity = PythonActivity.mActivity
                notificationManager = NotificationManagerCompat.from_(activity)
                notificationManager.cancel(1001)
                logger.info("Android notification cleared successfully")
            except ImportError:
                logger.warning("pyjnius module not found; cannot clear notification.")
            except Exception as android_error:
                logger.error(f"Error clearing Android notification: {android_error}")
        except Exception as e:
            logger.error(f"Error in stop_background_playback: {e}")

    def update_album_grid(self):
        try:
            self.ids.album_grid.clear_widgets()
            for album in self.albums:
                item = AlbumGridItem(
                    source=album['cover'],
                    on_release=lambda x, a=album: self.show_album_details(a)
                )
                self.ids.album_grid.add_widget(item)
        except Exception as e:
            logger.error(f"Error in update_album_grid: {e}")

    def _load_cover_for_item(self, item_widget, song_path):
        """تحميل صورة الغلاف لعنصر القائمة بطريقة محسنة للأداء"""
        try:
            # استخدام ذاكرة التخزين المؤقت للصور
            if hasattr(self, '_cover_cache') and song_path in self._cover_cache:
                cover_path = self._cover_cache[song_path]
                if os.path.exists(cover_path):
                    # استخدام صورة الغلاف من الذاكرة المؤقتة
                    cover_image = AsyncImage(
                        source=cover_path,
                        size_hint=(None, None),
                        size=(dp(40), dp(40)),
                        pos_hint={'center_y': 0.5}
                    )
                    item_widget.add_widget(cover_image)
                    return

            # جدولة تحميل الصورة في الإطار التالي
            def _load_cover(dt):
                try:
                    # إنشاء صورة الغلاف الافتراضية مقدمًا للتأكد من وجودها
                    if not hasattr(self, 'default_album_cover') or not self.default_album_cover or not os.path.exists(self.default_album_cover):
                        self.default_album_cover = self.create_empty_cover()

                    # الحصول على مسار صورة الغلاف
                    cover_path = self.get_album_cover(song_path)

                    if cover_path and os.path.exists(cover_path) and self.is_valid_image(cover_path):
                        # استخدام صورة الغلاف
                        cover_image = AsyncImage(
                            source=cover_path,
                            size_hint=(None, None),
                            size=(dp(40), dp(40)),
                            pos_hint={'center_y': 0.5}
                        )
                        item_widget.add_widget(cover_image)
                    else:
                        # استخدام صورة الغلاف الافتراضية
                        self._add_default_cover_to_item(item_widget)
                except Exception as e:
                    logger.error(f"Error loading cover image: {e}")
                    # استخدام صورة الغلاف الافتراضية في حالة الخطأ
                    self._add_default_cover_to_item(item_widget)

            # جدولة تحميل الصورة بتأخير قصير
            Clock.schedule_once(_load_cover, 0.1)
        except Exception as e:
            logger.error(f"Error in _load_cover_for_item: {e}")
            # استخدام صورة الغلاف الافتراضية في حالة الخطأ
            self._add_default_cover_to_item(item_widget)

    def _add_default_cover_to_item(self, item_widget):
        """إضافة صورة الغلاف الافتراضية لعنصر القائمة"""
        try:
            # التحقق من وجود صورة الغلاف الافتراضية
            if hasattr(self, 'default_album_cover') and self.default_album_cover and os.path.exists(self.default_album_cover):
                # استخدام صورة الغلاف الافتراضية
                cover_image = AsyncImage(
                    source=self.default_album_cover,
                    size_hint=(None, None),
                    size=(dp(40), dp(40)),
                    pos_hint={'center_y': 0.5}
                )
                item_widget.add_widget(cover_image)
            else:
                # إنشاء مربع أزرق بسيط
                from kivy.uix.widget import Widget
                from kivy.graphics import Color, Rectangle

                blue_box = Widget(
                    size_hint=(None, None),
                    size=(dp(40), dp(40)),
                    pos_hint={'center_y': 0.5}
                )

                with blue_box.canvas:
                    Color(0.2, 0.6, 1, 1)  # لون أزرق
                    Rectangle(pos=(0, 0), size=(dp(40), dp(40)))

                item_widget.add_widget(blue_box)
        except Exception as e:
            logger.error(f"Error adding default cover to item: {e}")

    def normalize_audio_format(self, path):
        """Convert audio to standardized format to prevent channel layout issues"""
        try:
            # Check if file exists
            if not path or not os.path.exists(path):
                logger.error(f"File not found: {path}")
                return None

            # Get file extension
            file_ext = os.path.splitext(path)[1].lower()

            # Check if format is supported
            if file_ext not in self.supported_formats:
                logger.warning(f"Unsupported audio format: {file_ext}")
                return None

            # For MP3 files, just return the original path as they are well-supported
            if file_ext == '.mp3':
                logger.info(f"MP3 format detected, no conversion needed: {path}")
                return path

            # For WAV files, just return the original path as they are well-supported
            if file_ext == '.wav':
                logger.info(f"WAV format detected, no conversion needed: {path}")
                return path

            # Create temporary directory for conversions
            temp_dir = os.path.join(self.get_app_data_dir(), 'converted_audio')
            os.makedirs(temp_dir, exist_ok=True)

            # Create unique filename
            file_hash = hashlib.md5(path.encode()).hexdigest()
            output_path = os.path.join(temp_dir, f"{file_hash}.mp3")  # Convert to MP3 instead of WAV

            # If file already exists
            if os.path.exists(output_path):
                logger.info(f"Using existing converted file: {output_path}")
                return output_path

            # Try to convert using FFmpeg
            try:
                # Use FFmpeg for conversion
                ffmpeg_path = self.get_ffmpeg_path()
                if not ffmpeg_path:
                    logger.warning("FFmpeg not available, using original file")
                    return path

                # Convert to MP3 format
                cmd = [
                    ffmpeg_path,
                    '-y',
                    '-i', path,
                    '-c:a', 'libmp3lame',  # Use MP3 codec
                    '-b:a', '192k',        # Set bitrate
                    '-ac', '2',            # Set channels
                    '-ar', '44100',        # Set sample rate
                    output_path
                ]

                # Execute conversion in background
                creation_flags = 0
                if platform == 'win':
                    creation_flags = subprocess.CREATE_NO_WINDOW

                logger.info(f"Converting file: {path} to {output_path}")
                result = subprocess.run(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=creation_flags
                )

                if result.returncode != 0:
                    logger.error(f"FFmpeg conversion failed: {result.stderr.decode()}")
                    return path

                # Check if output file exists and has size > 0
                if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
                    logger.info(f"Conversion successful: {output_path}")
                    return output_path
                else:
                    logger.error(f"Conversion failed: Output file is empty or does not exist")
                    return path

            except Exception as conversion_error:
                logger.error(f"Error during conversion: {conversion_error}")
                return path

        except Exception as e:
            logger.error(f"Audio normalization error: {e}")
            return path

    def get_ffmpeg_path(self):
        """الحصول على مسار FFmpeg المناسب للأندرويد"""
        try:
            # 1. تحقق من وجود FFmpeg في مجلد التطبيق
            app_dir = os.path.dirname(os.path.abspath(__file__))
            ffmpeg_in_app_dir = os.path.join(app_dir, 'ffmpeg')

            if os.path.exists(ffmpeg_in_app_dir):
                logger.info(f"FFmpeg found in app directory: {ffmpeg_in_app_dir}")
                return ffmpeg_in_app_dir

            # 2. تحقق من وجود FFmpeg في المجلد الحالي
            current_dir_ffmpeg = 'ffmpeg'
            if os.path.exists(current_dir_ffmpeg):
                logger.info(f"FFmpeg found in current directory: {current_dir_ffmpeg}")
                return current_dir_ffmpeg

            # 3. على Android، حاول استخدام FFmpeg المضمن
            try:
                from jnius import autoclass
                PythonActivity = autoclass('org.kivy.android.PythonActivity')
                android_ffmpeg = os.path.join(PythonActivity.getFilesDir().getAbsolutePath(), 'ffmpeg')
                if os.path.exists(android_ffmpeg):
                    logger.info(f"FFmpeg found on Android: {android_ffmpeg}")
                    return android_ffmpeg
            except Exception as android_error:
                logger.warning(f"Error accessing Android FFmpeg: {android_error}")

            # 4. البحث في مواقع إضافية على Android
            common_android_paths = [
                '/system/bin/ffmpeg',
                '/system/xbin/ffmpeg',
                '/data/data/org.kivy.android/files/ffmpeg'
            ]

            for path in common_android_paths:
                if os.path.exists(path):
                    logger.info(f"FFmpeg found in Android location: {path}")
                    return path

            # لم يتم العثور على FFmpeg
            logger.warning("FFmpeg not found on Android system")
            return None

        except Exception as e:
            logger.error(f"Error getting FFmpeg path: {e}")
            return None

    def clean_corrupted_covers(self, dt=None):
        """تنظيف وإصلاح صور الأغلفة التالفة"""
        try:
            logger.info("بدء تنظيف وإصلاح صور الأغلفة التالفة")

            # تحديد مجلد الأغلفة
            covers_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'album_covers')
            if not os.path.exists(covers_dir):
                logger.info("مجلد الأغلفة غير موجود، إنشاء المجلد")
                try:
                    os.makedirs(covers_dir, exist_ok=True)
                except Exception as e:
                    logger.error(f"خطأ في إنشاء مجلد الأغلفة: {e}")
                return

            # الحصول على قائمة بجميع ملفات الأغلفة
            try:
                cover_files = [os.path.join(covers_dir, f) for f in os.listdir(covers_dir)
                              if os.path.isfile(os.path.join(covers_dir, f)) and f.lower().endswith(('.jpg', '.png', '.jpeg'))]
            except Exception as e:
                logger.error(f"خطأ في قراءة محتويات مجلد الأغلفة: {e}")
                return

            logger.info(f"تم العثور على {len(cover_files)} ملف غلاف")

            # التحقق من كل ملف
            corrupted_count = 0
            repaired_count = 0
            deleted_count = 0

            for cover_file in cover_files:
                try:
                    # التحقق من صحة الصورة
                    if not self.is_valid_image(cover_file):
                        logger.warning(f"تم اكتشاف صورة غلاف تالفة: {cover_file}")

                        # محاولة إصلاح الصورة
                        repaired_path = self.repair_corrupted_cover(cover_file)

                        if repaired_path and os.path.exists(repaired_path) and self.is_valid_image(repaired_path):
                            # تم إصلاح الصورة بنجاح
                            logger.info(f"تم إصلاح صورة الغلاف بنجاح: {repaired_path}")
                            repaired_count += 1
                        else:
                            # فشل إصلاح الصورة، حذفها
                            logger.warning(f"فشل إصلاح صورة الغلاف، جاري الحذف: {cover_file}")
                            try:
                                if os.path.exists(cover_file):
                                    os.remove(cover_file)
                                    deleted_count += 1
                            except Exception as e:
                                logger.error(f"خطأ في حذف صورة الغلاف التالفة: {e}")

                        corrupted_count += 1
                except Exception as e:
                    logger.error(f"خطأ في التحقق من صحة الصورة: {cover_file}, {e}")

            logger.info(f"إحصائيات تنظيف الأغلفة: {corrupted_count} تالفة، {repaired_count} تم إصلاحها، {deleted_count} تم حذفها")

            # إعادة تعيين ذاكرة التخزين المؤقت للصور
            if hasattr(self, '_cover_cache'):
                self._cover_cache = {}
                logger.info("تم إعادة تعيين ذاكرة التخزين المؤقت للصور")

            # تعيين صورة الغلاف الافتراضية من مجلد الصور
            default_cover = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'images', 'default_album_cover.png')
            if os.path.exists(default_cover) and self.is_valid_image(default_cover):
                self.default_album_cover = default_cover
                logger.info(f"تم تعيين صورة الغلاف الافتراضية من مجلد الصور: {default_cover}")
            # إنشاء صورة الغلاف الافتراضية إذا لم تكن موجودة
            elif not hasattr(self, 'default_album_cover') or not self.default_album_cover or not os.path.exists(self.default_album_cover):
                self.default_album_cover = self.create_empty_cover()
                logger.info(f"تم إنشاء صورة الغلاف الافتراضية: {self.default_album_cover}")

        except Exception as e:
            logger.error(f"خطأ في تنظيف صور الأغلفة التالفة: {e}")
            import traceback
            logger.error(traceback.format_exc())



    def get_song_title(self, path):
        """الحصول على عنوان الأغنية من الملف الصوتي"""
        # استخدام ذاكرة التخزين المؤقت للعناوين
        if hasattr(self, '_title_cache') and path in self._title_cache:
            return self._title_cache[path]

        # إنشاء ذاكرة التخزين المؤقت إذا لم تكن موجودة
        if not hasattr(self, '_title_cache'):
            self._title_cache = {}

        result = None
        try:
            if not path or not os.path.exists(path):
                self._title_cache[path] = "Unknown Track"
                return "Unknown Track"

            # استخراج العنوان من الملف
            file_ext = os.path.splitext(path)[1].lower()

            # استخدام طريقة مختلفة حسب نوع الملف
            if file_ext == '.mp3':
                try:
                    id3 = ID3(path)
                    title_tag = id3.get('TIT2')
                    if title_tag and title_tag.text:
                        result = title_tag.text[0]
                except:
                    pass
            elif file_ext == '.flac':
                try:
                    audio = FLAC(path)
                    if 'title' in audio:
                        result = audio['title'][0]
                except:
                    pass
            elif file_ext in ['.m4a', '.mp4', '.aac']:
                try:
                    audio = MP4(path)
                    if '\xa9nam' in audio:
                        result = audio['\xa9nam'][0]
                except:
                    pass
            elif file_ext in ['.ogg', '.opus']:
                try:
                    if file_ext == '.ogg':
                        audio = OggVorbis(path)
                    else:
                        audio = OggOpus(path)
                    if 'title' in audio:
                        result = audio['title'][0]
                except:
                    pass
            elif file_ext in ['.wma', '.asf']:
                try:
                    audio = ASF(path)
                    if 'Title' in audio:
                        result = str(audio['Title'][0])
                except:
                    pass

            # إذا لم يتم العثور على عنوان، استخدم اسم الملف
            if not result:
                result = os.path.basename(path)

            # تحويل النتيجة إلى نص إذا لم تكن كذلك
            if not isinstance(result, str):
                try:
                    result = result.decode('utf-8', errors='replace')
                except Exception as decode_err:
                    logger.debug(f"Decoding error for title in {path}: {decode_err}")
                    result = str(result)

            # إزالة امتداد الملف إذا كانت النتيجة هي اسم الملف
            if result == os.path.basename(path):
                result = os.path.splitext(result)[0]

            # تنظيف العنوان
            result = result.strip()
            if not result:
                result = os.path.splitext(os.path.basename(path))[0]

            # معالجة النص العربي
            display_text = result
            if any(ord(c) >= 0x0600 and ord(c) <= 0x06FF for c in display_text):
                try:
                    # محاولة استخدام arabic_reshaper إذا كان متاحًا
                    try:
                        import arabic_reshaper
                        from bidi.algorithm import get_display
                        reshaped_text = arabic_reshaper.reshape(display_text)
                        display_text = get_display(reshaped_text)
                    except ImportError:
                        # إذا لم يكن متاحًا، استخدم النص كما هو
                        pass
                except:
                    pass

            # تخزين العنوان في ذاكرة التخزين المؤقت
            self._title_cache[path] = display_text
            return display_text

        except:
            # في حالة حدوث خطأ، استخدم اسم الملف بدون الامتداد
            try:
                display_text = os.path.splitext(os.path.basename(path))[0]
                self._title_cache[path] = display_text
                return display_text
            except:
                self._title_cache[path] = "Unknown Track"
                return "Unknown Track"

    def show_song_settings_menu(self, button, path):
        """عرض قائمة إعدادات الأغنية"""
        try:
            # استخدام نصوص إنجليزية بسيطة
            menu_items = [
                {
                    "text": "Favorite" if path not in self.favorites else "Unfavorite",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=path: self.toggle_favorite(x),
                    "height": dp(48),
                    "font_name": "Roboto"  # استخدام خط Roboto الافتراضي
                },
                {
                    "text": "Delete",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=path: self.show_delete_confirmation(x),
                    "height": dp(48),
                    "font_name": "Roboto"  # استخدام خط Roboto الافتراضي
                },
                {
                    "text": "Details",
                    "viewclass": "OneLineListItem",
                    "on_release": lambda x=path: self.show_song_details(x),
                    "height": dp(48),
                    "font_name": "Roboto"  # استخدام خط Roboto الافتراضي
                }
            ]

            # إنشاء القائمة المنسدلة
            self.settings_menu = MDDropdownMenu(
                caller=button,
                items=menu_items,
                width_mult=2,  # عرض مناسب للنصوص الإنجليزية
                max_height=dp(200),
                background_color=self.get_bg_color(),
                radius=[dp(10), dp(10), dp(10), dp(10)],  # إضافة حواف مستديرة
                elevation=4,  # إضافة ظل
            )

            # فتح القائمة
            self.settings_menu.open()
        except Exception as e:
            logger.error(f"Error showing song settings menu: {e}")

    def handle_long_press(self, song_item):
        def delete_action(instance):
            try:
                self.show_delete_confirmation(song_item.file_path)
                dialog.dismiss()
            except Exception as e:
                logger.error(f"Error in delete_action: {e}")

        def toggle_favorite_action(instance):
            try:
                self.toggle_favorite(song_item.file_path)
                dialog.dismiss()
            except Exception as e:
                logger.error(f"Error in toggle_favorite_action: {e}")

        def details_action(instance):
            try:
                self.show_song_details(song_item.file_path)
                dialog.dismiss()
            except Exception as e:
                logger.error(f"Error in details_action: {e}")

        dialog = MDDialog(
            title="Song Options",
            text=f"Options for: {os.path.basename(song_item.file_path)}",
            buttons=[
                MDFlatButton(
                    text="Delete",
                    theme_text_color="Custom",
                    text_color=self.get_error_color(),
                    on_release=delete_action,
                    font_name=self.current_font
                ),
                MDFlatButton(
                    text="Toggle Favorite",
                    theme_text_color="Custom",
                    text_color=self.get_primary_color(),
                    on_release=toggle_favorite_action,
                    font_name=self.current_font
                ),
                MDFlatButton(
                    text="Details",
                    theme_text_color="Custom",
                    text_color=self.get_primary_color(),
                    on_release=details_action,
                    font_name=self.current_font
                )
            ]
        )
        dialog.open()

    def show_song_details(self, path):
        try:
            if not path or not os.path.exists(path):
                self.show_error_dialog("File not found!")
                return

            try:
                audio = MP3(path)
                title = self.get_song_title(path)

                try:
                    tags = ID3(path)
                    artist = str(tags.get('TPE1', 'Unknown Artist'))
                    album = str(tags.get('TALB', 'Unknown Album'))
                except:
                    artist = 'Unknown Artist'
                    album = 'Unknown Album'

                info = f"Title: {title}\n" \
                       f"Artist: {artist}\n" \
                       f"Album: {album}\n" \
                       f"Duration: {self.format_time(audio.info.length)}\n" \
                       f"Bitrate: {int(audio.info.bitrate / 1000)} kbps\n" \
                       f"Sample Rate: {audio.info.sample_rate} Hz"

                if path in self.favorites:
                    info += "\n\nThis song is in your favorites ❤️"
            except Exception as e:
                info = f"Error getting details: {str(e)}"

            dialog = MDDialog(
                title="Song Details",
                text=info,
                buttons=[
                    MDFlatButton(
                        text="OK",
                        theme_text_color="Custom",
                        text_color=self.get_primary_color(),
                        on_release=lambda x: dialog.dismiss(),
                        font_name=self.current_font
                    )
                ]
            )
            dialog.open()
        except Exception as e:
            logger.error(f"Error in show_song_details: {e}")

    def show_current_song_details(self):
        """عرض تفاصيل المسار الحالي"""
        try:
            if self.current_index >= 0:
                playlist_to_use = self.favorites if self.is_favorites_visible else self.playlist
                if self.current_index < len(playlist_to_use):
                    current_path = playlist_to_use[self.current_index]
                    self.show_song_details(current_path)
                else:
                    self.show_error_dialog("No track is currently selected")
            else:
                self.show_error_dialog("No track is currently playing")
        except Exception as e:
            logger.error(f"Error in show_current_song_details: {e}")

    def toggle_favorite_current(self):
        """إضافة/إزالة المسار الحالي من المفضلة"""
        try:
            if self.current_index >= 0:
                playlist_to_use = self.favorites if self.is_favorites_visible else self.playlist
                if self.current_index < len(playlist_to_use):
                    current_path = playlist_to_use[self.current_index]
                    self.toggle_favorite(current_path)
                else:
                    self.show_error_dialog("No track is currently selected")
            else:
                self.show_error_dialog("No track is currently playing")
        except Exception as e:
            logger.error(f"Error in toggle_favorite_current: {e}")

    @mainthread
    def update(self, dt):
        try:
            if self.sound and self.sound.state == 'play':
                current_time = self.sound.get_pos()
                self.ids.seek_slider_now_playing.value = current_time
        except Exception as e:
            logger.error(f"Error in update: {e}")

class MusicPlayerApp(MDApp):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Modern theme configuration
        self.theme_cls.primary_palette = "DeepPurple"
        self.theme_cls.accent_palette = "Amber"
        self.theme_cls.theme_style = "Dark"
        self.theme_cls.material_style = "M3"

        # Enhanced theme properties for modern UI
        self.theme_cls.primary_hue = "500"  # More vibrant primary color
        self.theme_cls.accent_hue = "A400"  # Brighter accent color
        self.theme_cls.roundness = 15       # Rounded corners for all components

        # Variable to store shared URL
        self.shared_url = None

        # Try to import Arabic text support libraries
        try:
            import arabic_reshaper
            import bidi.algorithm
            logger.info("Arabic text support libraries loaded successfully")
        except ImportError:
            logger.warning("Arabic text support libraries not available")

    def on_start(self):
        try:
            # === ENHANCED PERMISSION REQUEST ===
            logger.info("🚀 App starting - requesting permissions...")

            # استخدام مدير الأذونات المحسن
            try:
                from permission_manager import permission_manager

                def permission_result_callback(success, message):
                    logger.info(f"Permission request result: {success} - {message}")
                    if not success:
                        logger.warning("Some permissions were denied")

                # طلب الأذونات الأساسية فوراً
                permission_manager.request_essential_permissions_only(permission_result_callback)

                # طلب جميع الأذونات بعد ثانيتين
                Clock.schedule_once(
                    lambda dt: permission_manager.request_all_permissions(permission_result_callback),
                    2.0
                )

                # جدولة فحص دوري
                permission_manager.schedule_periodic_check(interval=30)

            except Exception as e:
                logger.error(f"Error with permission manager: {e}")
                # fallback إلى الطريقة القديمة
                force_permission_request()
                Clock.schedule_once(lambda dt: request_permissions_safely(), 2.0)

            # Initialize performance optimizer
            try:
                from performance_optimizer import PerformanceOptimizer
                self.performance_optimizer = PerformanceOptimizer(self)
                self.performance_optimizer.start()
                logger.info("Performance optimizer initialized")
            except Exception as e:
                logger.warning(f"Could not initialize performance optimizer: {e}")

            # === ADDITIONAL PERMISSION CHECKS ===
            # فحص دوري للأذونات كل 30 ثانية
            def periodic_permission_check(dt):
                try:
                    if ANDROID_PERMISSIONS_AVAILABLE:
                        essential_perms = [
                            Permission.READ_EXTERNAL_STORAGE,
                            Permission.WRITE_EXTERNAL_STORAGE
                        ]

                        missing = []
                        for perm in essential_perms:
                            if not check_permission(perm):
                                missing.append(perm)

                        if missing:
                            logger.warning(f"⚠️ Still missing permissions: {missing}")
                            request_permissions_safely()
                except Exception as e:
                    logger.error(f"Error in periodic permission check: {e}")

            Clock.schedule_interval(periodic_permission_check, 30.0)

            # Check for shared URL from intent
            self.check_for_shared_url()

            # تفعيل ملء الشاشة للأندرويد
            Window.fullscreen = 'auto'
            Clock.schedule_once(self.adjust_layout_for_screen, 0.1)

            # Log performance stats after startup
            Clock.schedule_once(lambda dt: self.log_performance_stats(), 5)
        except Exception as e:
            logging.error(f"Error in on_start: {e}")

    def log_performance_stats(self):
        """Log performance statistics"""
        if hasattr(self, 'performance_optimizer'):
            self.performance_optimizer.log_performance_stats()

    def check_for_shared_url(self):
        """التحقق من وجود رابط مشارك عند تشغيل التطبيق على الأندرويد"""

        try:
            from jnius import autoclass
            PythonActivity = autoclass('org.kivy.android.PythonActivity')
            activity = PythonActivity.mActivity
            Intent = autoclass('android.content.Intent')
            Uri = autoclass('android.net.Uri')

            # Get the intent that started this activity
            intent = activity.getIntent()
            action = intent.getAction()

            logger.info(f"App started with intent action: {action}")

            # Handle SEND action (shared URLs)
            if action == Intent.ACTION_SEND:
                mime_type = intent.getType()
                logger.info(f"Received shared content with MIME type: {mime_type}")

                if mime_type and mime_type.startswith('text/'):
                    shared_text = intent.getStringExtra(Intent.EXTRA_TEXT)
                    if shared_text:
                        logger.info(f"Received shared text: {shared_text}")
                        self.shared_url = shared_text

                        # Check if it's a YouTube URL
                        if "youtube.com" in shared_text or "youtu.be" in shared_text:
                            logger.info("Detected YouTube URL in shared text")

                        # Schedule processing of the shared URL
                        Clock.schedule_once(self.process_shared_url, 2)

            # Handle VIEW action (clicked links)
            elif action == Intent.ACTION_VIEW:
                uri = intent.getData()
                if uri:
                    url = uri.toString()
                    logger.info(f"Received VIEW intent with URL: {url}")
                    self.shared_url = url

                    # Check if it's a YouTube URL
                    if "youtube.com" in url or "youtu.be" in url:
                        logger.info("Detected YouTube URL in VIEW intent")

                    # Schedule processing of the shared URL
                    Clock.schedule_once(self.process_shared_url, 2)

            # Handle PROCESS_TEXT action (selected text)
            elif action == Intent.ACTION_PROCESS_TEXT:
                if intent.hasExtra(Intent.EXTRA_PROCESS_TEXT):
                    process_text = intent.getStringExtra(Intent.EXTRA_PROCESS_TEXT)
                    if process_text:
                        logger.info(f"Received process text: {process_text}")
                        self.shared_url = process_text

                        # Check if it's a YouTube URL
                        if "youtube.com" in process_text or "youtu.be" in process_text:
                            logger.info("Detected YouTube URL in process text")

                        # Schedule processing of the shared URL
                        Clock.schedule_once(self.process_shared_url, 2)

            # Check for extras from ShareTargetService
            extras = intent.getExtras()
            if extras and extras.containsKey("action"):
                action_type = extras.getString("action")
                logger.info(f"Received action from ShareTargetService: {action_type}")

                if action_type == "download_youtube" and intent.hasExtra(Intent.EXTRA_TEXT):
                    shared_text = intent.getStringExtra(Intent.EXTRA_TEXT)
                    if shared_text:
                        logger.info(f"Handling YouTube download from ShareTargetService: {shared_text}")
                        self.shared_url = shared_text
                        Clock.schedule_once(self.process_shared_url, 2)

                elif action_type == "download_audio" and intent.hasExtra(Intent.EXTRA_TEXT):
                    shared_text = intent.getStringExtra(Intent.EXTRA_TEXT)
                    if shared_text:
                        logger.info(f"Handling audio download from ShareTargetService: {shared_text}")
                        self.shared_url = shared_text
                        Clock.schedule_once(self.process_shared_url, 2)
        except Exception as e:
            logger.error(f"Error checking for shared URL: {e}")
            import traceback
            logger.error(traceback.format_exc())

    def process_shared_url(self, dt):
        """Process the shared URL by sending it to the download manager"""
        if not self.shared_url:
            return

        try:
            # Get the root widget (MusicPlayer instance)
            root = self.root

            # Navigate to the downloads screen
            if hasattr(root.ids, 'screen_manager'):
                # Get the download screen
                download_screen = None
                for screen in root.ids.screen_manager.screens:
                    if screen.name == 'downloads':
                        download_screen = screen
                        break

                if download_screen and download_screen.download_manager:
                    # Switch to the downloads screen
                    root.ids.screen_manager.current = 'downloads'

                    # Add the URL to the download manager
                    success, download_item = download_screen.download_manager.add_download(self.shared_url)

                    if success:
                        # Show a success message
                        Snackbar(
                            text=f"Download started for shared URL",
                            duration=3
                        ).open()

                        # Update the download list
                        download_screen.update_download_list()

                        # If it's a YouTube URL, the format dialog will be shown automatically
                    else:
                        # Show an error message
                        Snackbar(
                            text="Failed to download shared URL",
                            duration=3
                        ).open()
                else:
                    logger.error("Download screen or download manager not found")
            else:
                logger.error("Screen manager not found in root widget")

            # Clear the shared URL to prevent processing it again
            self.shared_url = None
        except Exception as e:
            logger.error(f"Error processing shared URL: {e}")

    def build(self):
        # === SAFE KV FILE LOADING ===
        def safe_load_kv(filename):
            """تحميل ملف KV بشكل آمن"""
            try:
                if os.path.exists(filename):
                    Builder.load_file(filename)
                    logger.info(f"✅ Loaded KV file: {filename}")
                    return True
                else:
                    logger.warning(f"❌ KV file not found: {filename}")
                    return False
            except Exception as e:
                logger.error(f"❌ Error loading KV file {filename}: {e}")
                return False

        # Load main KV files safely
        safe_load_kv("MusicPlayer.kv")
        safe_load_kv("download_screen.kv")
        safe_load_kv("search_screen.kv")

        # Load audio settings screen safely
        safe_load_kv("audio_settings.kv")

        # Load modern UI components if available
        safe_load_kv("modern_components.kv")

        # === DEVICE COMPATIBILITY DETECTION ===
        def detect_device_capabilities():
            """كشف قدرات الجهاز للتوافق مع الأجهزة القديمة والحديثة"""
            capabilities = {
                'is_old_device': False,
                'api_level': 21,
                'memory_mb': 1024,
                'cpu_cores': 2,
                'supports_modern_features': True
            }

            try:
                if platform == 'android':
                    from jnius import autoclass
                    Build = autoclass('android.os.Build')
                    ActivityManager = autoclass('android.app.ActivityManager')
                    Context = autoclass('android.content.Context')
                    PythonActivity = autoclass('org.kivy.android.PythonActivity')

                    # Get API level
                    capabilities['api_level'] = Build.VERSION.SDK_INT

                    # Get memory info
                    activity = PythonActivity.mActivity
                    am = activity.getSystemService(Context.ACTIVITY_SERVICE)
                    mem_info = ActivityManager.MemoryInfo()
                    am.getMemoryInfo(mem_info)
                    capabilities['memory_mb'] = mem_info.totalMem / 1024 / 1024

                    # Detect old devices (API < 23 or low memory)
                    if capabilities['api_level'] < 23 or capabilities['memory_mb'] < 2000:
                        capabilities['is_old_device'] = True
                        capabilities['supports_modern_features'] = False

                    logger.info(f"Device: API {capabilities['api_level']}, Memory: {capabilities['memory_mb']:.0f}MB")

            except Exception as e:
                logger.warning(f"Could not detect device capabilities: {e}")

            return capabilities

        # Detect device capabilities
        device_capabilities = detect_device_capabilities()

        # === ADAPTIVE MEMORY CLEANUP ===
        def cleanup_memory():
            """تنظيف الذاكرة بشكل دوري مع التكيف حسب الجهاز"""
            try:
                import gc
                gc.collect()

                # More aggressive cleanup for old devices
                if device_capabilities['is_old_device']:
                    # Clear image caches more frequently
                    try:
                        from kivy.cache import Cache
                        Cache.remove('kv.image')
                        Cache.remove('kv.texture')
                    except:
                        pass

                logger.debug("Memory cleanup completed")
            except Exception as e:
                logger.error(f"Error during memory cleanup: {e}")

        # Adaptive cleanup interval based on device
        cleanup_interval = 30 if device_capabilities['is_old_device'] else 60
        Clock.schedule_interval(lambda dt: cleanup_memory(), cleanup_interval)

        # إنشاء الواجهة الرئيسية
        music_player = MusicPlayer()

        # تهيئة محسن الأداء بشكل آمن
        if PERFORMANCE_OPTIMIZER_AVAILABLE:
            try:
                music_player.performance_optimizer = PerformanceOptimizer(self)

                # جدولة بدء محسن الأداء بعد تهيئة التطبيق
                def start_optimizer(dt):
                    try:
                        if hasattr(music_player.performance_optimizer, 'start'):
                            music_player.performance_optimizer.start()
                        logger.info("✅ Performance optimizer started")
                    except Exception as opt_error:
                        logger.error(f"Error starting performance optimizer: {opt_error}")

                # بدء محسن الأداء بعد ثانية واحدة من بدء التطبيق
                Clock.schedule_once(start_optimizer, 1)

            except Exception as optimizer_error:
                logger.error(f"Error initializing performance optimizer: {optimizer_error}")
                # Use fallback
                music_player.performance_optimizer = PerformanceOptimizer(self)
        else:
            # Use fallback performance optimizer
            music_player.performance_optimizer = PerformanceOptimizer(self)
            logger.info("Using fallback performance optimizer")

        return music_player

    def adjust_layout_for_screen(self, dt):
        try:
            width, height = Window.size
            root = self.root

            if hasattr(root, 'adjust_layout'):
                root.adjust_layout(None, (width, height))
        except Exception as e:
            logging.error(f"Error in adjust_layout_for_screen: {e}")

    def on_stop(self):
        try:
            # Detener la reproducción en segundo plano
            self.root.stop_background_playback()

            # Limpiar archivos temporales
            if hasattr(self.root, 'cleanup_temp_files'):
                self.root.cleanup_temp_files()

            # Liberar memoria
            try:
                import gc
                gc.collect()
            except Exception as gc_error:
                logging.error(f"Error running garbage collection: {gc_error}")

        except Exception as e:
            logging.error(f"Error in on_stop: {e}")

    def __getattr__(self, name):
        """Handle attribute access for compatibility with super().__getattr__"""
        # This prevents 'super' object has no attribute '__getattr__' error
        raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{name}'")

if __name__ == '__main__':
    MusicPlayerApp().run()
