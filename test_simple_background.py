#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط للخلفية المحسنة
"""

import os
import sys
import logging

# إعداد المسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# إعداد السجل
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_background_colors():
    """اختبار ألوان الخلفية"""
    try:
        logger.info("🎨 اختبار ألوان الخلفية...")
        
        # محاولة استيراد الكلاسات
        from main import MusicPlayer
        
        # إنشاء مثيل للاختبار
        player = MusicPlayer()
        
        # اختبار الألوان المختلفة
        logger.info("اختبار get_bg_color...")
        bg_color = player.get_bg_color()
        logger.info(f"✅ لون الخلفية الأساسي: {bg_color}")
        
        logger.info("اختبار get_dynamic_bg_color...")
        dynamic_color = player.get_dynamic_bg_color()
        logger.info(f"✅ لون الخلفية الديناميكي: {dynamic_color}")
        
        logger.info("اختبار get_gradient_bg_colors...")
        gradient_colors = player.get_gradient_bg_colors()
        logger.info(f"✅ ألوان الخلفية المتدرجة: {gradient_colors}")
        
        logger.info("اختبار get_pattern_bg_color...")
        pattern_colors = player.get_pattern_bg_color()
        logger.info(f"✅ ألوان نمط الخلفية: {pattern_colors}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الألوان: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def test_background_functions():
    """اختبار دوال الخلفية"""
    try:
        logger.info("🔧 اختبار دوال الخلفية...")
        
        from main import MusicPlayer
        player = MusicPlayer()
        
        # اختبار الدوال المختلفة
        logger.info("اختبار apply_enhanced_background...")
        player.apply_enhanced_background()
        logger.info("✅ تم تطبيق الخلفية المحسنة")
        
        logger.info("اختبار apply_minimal_background...")
        player.apply_minimal_background()
        logger.info("✅ تم تطبيق الخلفية البسيطة")
        
        logger.info("اختبار apply_normal_background...")
        player.apply_normal_background()
        logger.info("✅ تم تطبيق الخلفية العادية")
        
        logger.info("اختبار update_background_theme...")
        player.update_background_theme()
        logger.info("✅ تم تحديث ثيم الخلفية")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ خطأ في اختبار الدوال: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    logger.info("🚀 بدء اختبار نظام الخلفية المبسط...")
    
    # اختبار 1: ألوان الخلفية
    logger.info("\n📋 الاختبار 1: ألوان الخلفية")
    test1_result = test_background_colors()
    
    # اختبار 2: دوال الخلفية
    logger.info("\n📋 الاختبار 2: دوال الخلفية")
    test2_result = test_background_functions()
    
    # ملخص النتائج
    logger.info("\n📊 ملخص الاختبارات:")
    logger.info(f"   ألوان الخلفية: {'✅ نجح' if test1_result else '❌ فشل'}")
    logger.info(f"   دوال الخلفية: {'✅ نجح' if test2_result else '❌ فشل'}")
    
    if test1_result and test2_result:
        logger.info("\n🎉 جميع الاختبارات نجحت! نظام الخلفية يعمل بشكل صحيح.")
        logger.info("\n💡 نصائح للاستخدام:")
        logger.info("   1. استخدم 'Background Style' في قائمة الإعدادات لتغيير نمط الخلفية")
        logger.info("   2. الخلفية تتحدث تلقائياً عند تغيير الثيم")
        logger.info("   3. الخلفية تتكيف مع الأغنية الحالية")
        return True
    else:
        logger.error("\n💥 بعض الاختبارات فشلت. يرجى مراجعة التنفيذ.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
